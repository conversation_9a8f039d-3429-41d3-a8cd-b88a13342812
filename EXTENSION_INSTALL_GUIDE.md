# دليل تثبيت إضافة المتصفح

## 🎉 **تم إصلاح مشكلة الأيقونات!**

### ✅ **المشكلة التي تم حلها:**
- ❌ **المشكلة السابقة**: `Could not load icon 'icons/icon16.png'`
- ✅ **الحل**: تم إنشاء جميع الأيقونات المطلوبة (16x16, 32x32, 48x48, 128x128)

### 📁 **الملفات الجاهزة:**
```
browser_extension/
├── manifest.json          ✅ جاهز
├── background.js          ✅ جاهز  
├── content.js             ✅ جاهز
├── popup.html             ✅ جاهز
├── popup.js               ✅ جاهز
├── popup.css              ✅ جاهز
└── icons/
    ├── icon16.png         ✅ تم إنشاؤه
    ├── icon32.png         ✅ تم إنشاؤه
    ├── icon48.png         ✅ تم إنشاؤه
    └── icon128.png        ✅ تم إنشاؤه
```

## 🔧 **خطوات التثبيت:**

### **للمتصفحات المبنية على Chromium (Chrome, Edge, Brave):**

1. **افتح إعدادات الإضافات:**
   - Chrome: اذهب إلى `chrome://extensions/`
   - Edge: اذهب إلى `edge://extensions/`
   - Brave: اذهب إلى `brave://extensions/`

2. **فعّل وضع المطور:**
   - ابحث عن "Developer mode" أو "وضع المطور"
   - فعّل المفتاح في الزاوية العلوية اليمنى

3. **حمّل الإضافة:**
   - انقر "Load unpacked" أو "تحميل إضافة غير مُعبأة"
   - اختر مجلد `browser_extension`
   - انقر "Select Folder" أو "اختيار مجلد"

4. **تأكيد التثبيت:**
   - ستظهر الإضافة في القائمة
   - ستظهر أيقونة زرقاء مع سهم تحميل في شريط الأدوات

### **لمتصفح Firefox:**

1. **افتح صفحة التطوير:**
   - اذهب إلى `about:debugging`

2. **اختر "This Firefox":**
   - انقر على "This Firefox" من القائمة الجانبية

3. **حمّل الإضافة مؤقتاً:**
   - انقر "Load Temporary Add-on"
   - اختر ملف `manifest.json` من مجلد `browser_extension`

4. **تأكيد التثبيت:**
   - ستظهر الإضافة في قائمة الإضافات المؤقتة

## 🎯 **كيفية الاستخدام:**

### **1. تحميل من YouTube:**
1. **اذهب إلى أي فيديو YouTube**
2. **انقر على أيقونة الإضافة** في شريط الأدوات
3. **ستفتح نافذة منبثقة** مع تفاصيل الفيديو
4. **اختر الجودة** المطلوبة
5. **انقر "تحميل"** - سيُرسل للبرنامج الرئيسي

### **2. تحميل من TikTok:**
1. **اذهب إلى أي فيديو TikTok**
2. **انقر على أيقونة الإضافة**
3. **انقر "تحميل"** - سيُرسل للبرنامج الرئيسي

### **3. تحميل ملف مباشر:**
1. **في أي صفحة تحتوي على رابط ملف**
2. **انقر بالزر الأيمن على الرابط**
3. **اختر "تحميل بـ Python Download Manager"**

## 🔍 **استكشاف الأخطاء:**

### **إذا لم تظهر الأيقونة:**
1. تأكد من أن البرنامج الرئيسي يعمل
2. تحقق من أن الخادم يعمل على `http://localhost:9876`
3. أعد تحميل الإضافة

### **إذا لم يعمل التحميل:**
1. **تحقق من حالة البرنامج الرئيسي:**
   ```bash
   curl http://localhost:9876/ping
   ```
   يجب أن يعيد: `{"message":"Download manager is running","status":"ok"}`

2. **تحقق من السجلات:**
   - افتح Developer Tools (`F12`)
   - اذهب إلى تبويب Console
   - ابحث عن رسائل خطأ

### **إذا ظهرت رسالة خطأ في التثبيت:**
- تأكد من اختيار مجلد `browser_extension` وليس ملف منفرد
- تأكد من وجود ملف `manifest.json` في المجلد
- تأكد من وجود جميع ملفات الأيقونات

## 🚀 **الحالة الحالية:**

### **✅ جاهز للتثبيت:**
- 🟢 **الأيقونات**: تم إنشاؤها بنجاح
- 🟢 **Manifest**: صحيح ومحدث
- 🟢 **البرنامج الرئيسي**: يعمل على Terminal 8
- 🟢 **الخادم**: نشط على localhost:9876

### **📋 الخطوة التالية:**
1. **افتح Chrome** أو أي متصفح Chromium
2. **اذهب إلى** `chrome://extensions/`
3. **فعّل وضع المطور**
4. **انقر "Load unpacked"**
5. **اختر مجلد** `browser_extension`
6. **🎉 ستعمل الإضافة بنجاح!**

## 💡 **نصائح:**
- الإضافة تعمل فقط عندما يكون البرنامج الرئيسي مفتوحاً
- يمكن تحميل عدة ملفات في نفس الوقت
- التحميلات ستبدأ تلقائياً في البرنامج الرئيسي
