# دليل تثبيت إضافات المتصفحات - Python Download Manager

## 🌐 **المتصفحات المدعومة:**

### ✅ **Chrome** 
### 🦊 **Firefox**
### 🌊 **Microsoft Edge**
### 🌐 **Chromium**

---

## 📦 **ملفات الإضافات:**

```
browser_extensions/
├── chrome/          # Chrome (الأصلي)
├── firefox/         # Firefox (جديد)
├── edge/           # Microsoft Edge (جديد)
├── chromium/       # Chromium (جديد)
└── shared/         # ملفات مشتركة
```

---

## 🚀 **خطوات التثبيت:**

### 1️⃣ **Google Chrome:**

#### **الطريقة:**
1. افتح Chrome واذهب إلى: `chrome://extensions/`
2. فعل "Developer mode" في الزاوية العلوية اليمنى
3. انقر على "Load unpacked"
4. اختر مجلد `browser_extension/` (الأصلي)
5. ستظهر الإضافة في قائمة الإضافات

#### **التحقق:**
- ✅ أيقونة الإضافة تظهر في شريط الأدوات
- ✅ اذهب إلى YouTube وستجد أيقونة التحميل

---

### 2️⃣ **Mozilla Firefox:**

#### **الطريقة:**
1. افتح Firefox واذهب إلى: `about:debugging`
2. انقر على "This Firefox" في الجانب الأيسر
3. انقر على "Load Temporary Add-on..."
4. اختر ملف `browser_extensions/firefox/manifest.json`
5. ستظهر الإضافة في قائمة الإضافات المؤقتة

#### **ملاحظة مهمة:**
- 🔄 **إضافة مؤقتة**: ستختفي عند إعادة تشغيل Firefox
- 📦 **للتثبيت الدائم**: يجب توقيع الإضافة من Mozilla

#### **التحقق:**
- ✅ أيقونة الإضافة تظهر مع رمز 🦊
- ✅ اذهب إلى YouTube وستجد أيقونة التحميل

---

### 3️⃣ **Microsoft Edge:**

#### **الطريقة:**
1. افتح Edge واذهب إلى: `edge://extensions/`
2. فعل "Developer mode" في الجانب الأيسر
3. انقر على "Load unpacked"
4. اختر مجلد `browser_extensions/edge/`
5. ستظهر الإضافة في قائمة الإضافات

#### **التحقق:**
- ✅ أيقونة الإضافة تظهر مع رمز 🌊
- ✅ إشعارات Edge تعمل
- ✅ اذهب إلى YouTube وستجد أيقونة التحميل

---

### 4️⃣ **Chromium:**

#### **الطريقة:**
1. افتح Chromium واذهب إلى: `chrome://extensions/`
2. فعل "Developer mode" في الزاوية العلوية اليمنى
3. انقر على "Load unpacked"
4. اختر مجلد `browser_extensions/chromium/`
5. ستظهر الإضافة في قائمة الإضافات

#### **التحقق:**
- ✅ أيقونة الإضافة تظهر مع رمز 🌐
- ✅ إشعارات Chromium تعمل
- ✅ اذهب إلى YouTube وستجد أيقونة التحميل

---

## 🔧 **الاختلافات بين المتصفحات:**

### **Chrome & Edge & Chromium:**
- **Manifest V3**: أحدث إصدار
- **Service Workers**: للخلفية
- **chrome.* APIs**: واجهات برمجة Chrome

### **Firefox:**
- **Manifest V2**: الإصدار المدعوم
- **Background Scripts**: للخلفية
- **browser.* APIs**: واجهات برمجة Firefox

---

## 🎨 **الميزات المميزة لكل متصفح:**

### **🔵 Chrome:**
- ✅ دعم كامل لجميع الميزات
- ✅ إشعارات متقدمة
- ✅ تحديث تلقائي للأيقونة

### **🦊 Firefox:**
- ✅ دعم كامل للميزات الأساسية
- ✅ أمان محسن
- ✅ خصوصية أفضل

### **🌊 Edge:**
- ✅ تكامل مع Windows
- ✅ إشعارات نظام التشغيل
- ✅ أداء محسن

### **🌐 Chromium:**
- ✅ مفتوح المصدر
- ✅ قابلية تخصيص عالية
- ✅ أداء سريع

---

## 🛠️ **استكشاف الأخطاء:**

### **مشكلة: الإضافة لا تظهر**
```
الحل:
1. تأكد من تفعيل Developer mode
2. تحقق من مسار المجلد الصحيح
3. أعد تحميل الإضافة
```

### **مشكلة: أيقونة التحميل لا تظهر على YouTube**
```
الحل:
1. أعد تحميل صفحة YouTube
2. تأكد من تشغيل Python Download Manager
3. تحقق من console للأخطاء (F12)
```

### **مشكلة: خطأ في الاتصال**
```
الحل:
1. تأكد من تشغيل البرنامج على localhost:9876
2. تحقق من إعدادات الجدار الناري
3. جرب إعادة تشغيل البرنامج
```

---

## 📊 **اختبار الإضافات:**

### **اختبار أساسي:**
1. ✅ تثبيت الإضافة بنجاح
2. ✅ ظهور أيقونة في شريط الأدوات
3. ✅ ظهور أيقونة التحميل على YouTube
4. ✅ عمل القائمة المنسدلة للصيغ
5. ✅ نجاح التحميل

### **اختبار متقدم:**
1. ✅ عمل التخزين المؤقت
2. ✅ عمل الإشعارات
3. ✅ عمل timeout والتحويل التلقائي
4. ✅ عمل جميع الصيغ (42+ صيغة)

---

## 🎯 **نصائح للاستخدام:**

### **لجميع المتصفحات:**
- 🔄 **أعد تحميل الصفحة** بعد تثبيت الإضافة
- 🎬 **اذهب إلى فيديو YouTube** لاختبار الإضافة
- ⚡ **تأكد من تشغيل البرنامج** قبل الاستخدام

### **لـ Firefox خاصة:**
- 🔄 **أعد تحميل الإضافة** بعد إعادة تشغيل المتصفح
- 📦 **استخدم about:debugging** لإدارة الإضافة

### **لـ Edge خاصة:**
- 🔔 **فعل الإشعارات** في إعدادات Windows
- 🎯 **استخدم أيقونة الإضافة** للوصول السريع

---

## 🎉 **النتيجة:**

**الآن لديك Python Download Manager يعمل على جميع المتصفحات الرئيسية!**

- 🔵 **Chrome**: الإصدار الأصلي المحسن
- 🦊 **Firefox**: إصدار متوافق مع WebExtensions
- 🌊 **Edge**: إصدار محسن مع تكامل Windows
- 🌐 **Chromium**: إصدار مفتوح المصدر

**استمتع بتحميل الفيديوهات من أي متصفح تفضله!** 🚀

---

**تاريخ الإنشاء**: 30 يونيو 2025
**الحالة**: 🟢 جاهز للاستخدام
**الدعم**: جميع المتصفحات الرئيسية
