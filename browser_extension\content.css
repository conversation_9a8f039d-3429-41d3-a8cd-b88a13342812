/* Content script styles for Python Download Manager */

.pdm-download-btn {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    z-index: 9999 !important;
    position: relative !important;
}

.pdm-youtube-btn {
    background: #f1f1f1 !important;
    border: none !important;
    border-radius: 18px !important;
    color: #030303 !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin: 0 8px !important;
    transition: background-color 0.1s !important;
}

.pdm-youtube-btn:hover {
    background: #e5e5e5 !important;
}

.pdm-tiktok-btn {
    background: rgba(255, 255, 255, 0.12) !important;
    border: none !important;
    border-radius: 50% !important;
    color: white !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 48px !important;
    height: 48px !important;
    margin: 8px 0 !important;
    transition: background-color 0.2s !important;
}

.pdm-tiktok-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.pdm-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.8) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 10000 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    direction: rtl !important;
}

.pdm-modal-content {
    background: white !important;
    border-radius: 12px !important;
    padding: 0 !important;
    max-width: 450px !important;
    width: 90% !important;
    max-height: 80vh !important;
    overflow: hidden !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
    animation: pdm-modal-appear 0.3s ease-out !important;
}

@keyframes pdm-modal-appear {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.pdm-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.pdm-modal-header h3 {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.pdm-close-btn {
    background: none !important;
    border: none !important;
    color: white !important;
    font-size: 24px !important;
    cursor: pointer !important;
    padding: 0 !important;
    width: 30px !important;
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    transition: background-color 0.2s !important;
}

.pdm-close-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.pdm-modal-body {
    padding: 24px !important;
}

.pdm-video-info {
    margin-bottom: 24px !important;
    padding: 16px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border-right: 4px solid #667eea !important;
}

.pdm-video-info h4 {
    margin: 0 0 12px 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #333 !important;
    line-height: 1.4 !important;
}

.pdm-video-info p {
    margin: 6px 0 !important;
    font-size: 14px !important;
    color: #666 !important;
}

.pdm-quality-selection {
    margin-bottom: 24px !important;
}

.pdm-quality-selection label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 500 !important;
    color: #555 !important;
    font-size: 14px !important;
}

.pdm-quality-select {
    width: 100% !important;
    padding: 12px 16px !important;
    border: 2px solid #e0e0e0 !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    background: white !important;
    cursor: pointer !important;
    transition: border-color 0.2s !important;
}

.pdm-quality-select:focus {
    outline: none !important;
    border-color: #667eea !important;
}

.pdm-actions {
    display: flex !important;
    gap: 12px !important;
}

.pdm-download-video-btn,
.pdm-download-audio-btn {
    flex: 1 !important;
    padding: 14px 20px !important;
    border: none !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

.pdm-download-video-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.pdm-download-video-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3) !important;
}

.pdm-download-audio-btn {
    background: #f8f9fa !important;
    color: #555 !important;
    border: 2px solid #e0e0e0 !important;
}

.pdm-download-audio-btn:hover {
    background: #e9ecef !important;
    border-color: #ced4da !important;
    transform: translateY(-1px) !important;
}

/* Responsive design */
@media (max-width: 480px) {
    .pdm-modal-content {
        width: 95% !important;
        margin: 20px !important;
    }
    
    .pdm-actions {
        flex-direction: column !important;
    }
    
    .pdm-modal-header {
        padding: 16px !important;
    }
    
    .pdm-modal-body {
        padding: 20px !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .pdm-modal-content {
        background: #2d3748 !important;
        color: #e2e8f0 !important;
    }
    
    .pdm-video-info {
        background: #4a5568 !important;
        color: #e2e8f0 !important;
    }
    
    .pdm-video-info h4 {
        color: #f7fafc !important;
    }
    
    .pdm-video-info p {
        color: #cbd5e0 !important;
    }
    
    .pdm-quality-select {
        background: #4a5568 !important;
        color: #e2e8f0 !important;
        border-color: #718096 !important;
    }
    
    .pdm-download-audio-btn {
        background: #4a5568 !important;
        color: #e2e8f0 !important;
        border-color: #718096 !important;
    }
    
    .pdm-download-audio-btn:hover {
        background: #2d3748 !important;
        border-color: #4a5568 !important;
    }
}

/* Animation for button appearance */
.pdm-download-btn {
    animation: pdm-button-appear 0.3s ease-out !important;
}

@keyframes pdm-button-appear {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading state */
.pdm-loading {
    position: relative !important;
    pointer-events: none !important;
    opacity: 0.7 !important;
}

.pdm-loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 16px !important;
    height: 16px !important;
    margin: -8px 0 0 -8px !important;
    border: 2px solid transparent !important;
    border-top: 2px solid currentColor !important;
    border-radius: 50% !important;
    animation: pdm-spin 1s linear infinite !important;
}

@keyframes pdm-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
