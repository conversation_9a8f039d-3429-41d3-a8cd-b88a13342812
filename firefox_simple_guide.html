<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تثبيت الإضافة في Firefox</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .step {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #FFD700;
        }
        
        .step-number {
            background: #FFD700;
            color: #333;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            margin-left: 15px;
        }
        
        .step-title {
            font-size: 1.3em;
            font-weight: bold;
            margin: 10px 0;
            display: inline-block;
        }
        
        .code-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .highlight {
            background: #FFD700;
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #FFC107;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .button {
            background: #FFD700;
            color: #333;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .button:hover {
            background: #FFA000;
            transform: translateY(-2px);
        }
        
        .screenshot {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            border: 2px dashed #FFD700;
        }
        
        .path {
            background: rgba(0, 0, 0, 0.4);
            padding: 8px 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9em;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦊 دليل تثبيت الإضافة في Firefox</h1>
        
        <div class="step">
            <span class="step-number">1</span>
            <span class="step-title">فتح صفحة التشخيص</span>
            
            <p>افتح Firefox وانقر على شريط العنوان، ثم اكتب:</p>
            <div class="code-box">about:debugging</div>
            <p>واضغط <span class="highlight">Enter</span></p>
            
            <div class="screenshot">
                📍 ستظهر صفحة "Firefox Developer Tools"
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            <span class="step-title">اختيار This Firefox</span>
            
            <p>في الصفحة التي ظهرت، ابحث عن:</p>
            <div class="code-box">This Firefox</div>
            <p>وانقر عليه</p>
            
            <div class="screenshot">
                📍 ستجده في الجانب الأيسر من الصفحة
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            <span class="step-title">تحميل الإضافة المؤقتة</span>
            
            <p>ابحث عن زر:</p>
            <div class="code-box">Load Temporary Add-on...</div>
            <p>وانقر عليه</p>
            
            <div class="screenshot">
                📍 ستجده تحت عنوان "Temporary Extensions"
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">4</span>
            <span class="step-title">اختيار ملف الإضافة</span>
            
            <p>ستفتح نافذة اختيار الملفات. انتقل إلى:</p>
            <div class="path">C:\Users\<USER>\Desktop\augment\python_download\browser_extensions\firefox\</div>
            
            <p>واختر ملف:</p>
            <div class="code-box">manifest.json</div>
            
            <div class="screenshot">
                📁 تأكد من اختيار ملف manifest.json وليس مجلد
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">5</span>
            <span class="step-title">التحقق من التثبيت</span>
            
            <p>بعد اختيار الملف، ستظهر الإضافة في القائمة:</p>
            <div class="code-box">🦊 Python Download Manager v1.0.0</div>
            
            <div class="success">
                ✅ <strong>نجح التثبيت!</strong> ستظهر أيقونة الإضافة في شريط الأدوات
            </div>
        </div>
        
        <div class="warning">
            ⚠️ <strong>ملاحظة مهمة:</strong><br>
            هذه إضافة مؤقتة وستختفي عند إعادة تشغيل Firefox.<br>
            لإعادة تحميلها، كرر نفس الخطوات أو انقر على "Reload" بجانب اسم الإضافة.
        </div>
        
        <div class="step">
            <span class="step-number">6</span>
            <span class="step-title">اختبار الإضافة</span>
            
            <p>للتأكد من عمل الإضافة:</p>
            <ol>
                <li>تأكد من تشغيل Python Download Manager</li>
                <li>اذهب إلى أي فيديو على YouTube</li>
                <li>ابحث عن أيقونة التحميل أعلى يسار الفيديو</li>
                <li>انقر عليها واختبر التحميل</li>
            </ol>
            
            <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" class="button" target="_blank">
                🎬 اختبر على YouTube
            </a>
        </div>
        
        <div class="step">
            <span class="step-number">💡</span>
            <span class="step-title">نصائح إضافية</span>
            
            <ul>
                <li><strong>للإزالة:</strong> انقر "Remove" بجانب اسم الإضافة</li>
                <li><strong>للفحص:</strong> انقر "Inspect" لفتح أدوات المطور</li>
                <li><strong>للتحديث:</strong> انقر "Reload" بعد تعديل ملفات الإضافة</li>
                <li><strong>للمساعدة:</strong> تحقق من Console (F12) للأخطاء</li>
            </ul>
        </div>
        
        <div class="success">
            🎉 <strong>تهانينا!</strong><br>
            الآن يمكنك تحميل الفيديوهات من YouTube مباشرة باستخدام Firefox!
        </div>
    </div>
    
    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.step').forEach(step => {
            step.addEventListener('mouseenter', () => {
                step.style.transform = 'translateX(-5px)';
                step.style.boxShadow = '5px 5px 15px rgba(0,0,0,0.3)';
            });
            
            step.addEventListener('mouseleave', () => {
                step.style.transform = 'translateX(0)';
                step.style.boxShadow = 'none';
            });
        });
        
        // نسخ المسار عند النقر
        document.querySelectorAll('.path').forEach(path => {
            path.style.cursor = 'pointer';
            path.title = 'انقر للنسخ';
            
            path.addEventListener('click', () => {
                navigator.clipboard.writeText(path.textContent).then(() => {
                    const originalText = path.textContent;
                    path.textContent = '✅ تم النسخ!';
                    path.style.background = 'rgba(76, 175, 80, 0.3)';
                    
                    setTimeout(() => {
                        path.textContent = originalText;
                        path.style.background = 'rgba(0, 0, 0, 0.4)';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>
