"""
URL Validation utilities
"""

import re
from urllib.parse import urlparse
import requests
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

class URLValidator:
    """URL validation and analysis utilities"""
    
    # Common URL patterns
    URL_PATTERN = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    # Video platform patterns
    YOUTUBE_PATTERNS = [
        r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]+)',
        r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]+)',
        r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]+)',
        r'(?:https?://)?(?:www\.)?youtube\.com/v/([a-zA-Z0-9_-]+)'
    ]
    
    TIKTOK_PATTERNS = [
        r'(?:https?://)?(?:www\.)?tiktok\.com/@[^/]+/video/(\d+)',
        r'(?:https?://)?vm\.tiktok\.com/([a-zA-Z0-9]+)',
        r'(?:https?://)?(?:www\.)?tiktok\.com/t/([a-zA-Z0-9]+)'
    ]
    
    @staticmethod
    def is_valid_url(url):
        """Check if URL is valid"""
        if not url or not isinstance(url, str):
            return False
        
        # Basic format check
        if not URLValidator.URL_PATTERN.match(url):
            return False
        
        # Parse URL
        try:
            parsed = urlparse(url)
            return all([parsed.scheme, parsed.netloc])
        except:
            return False
    
    @staticmethod
    def is_youtube_url(url):
        """Check if URL is from YouTube"""
        if not url:
            return False
        
        for pattern in URLValidator.YOUTUBE_PATTERNS:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def is_tiktok_url(url):
        """Check if URL is from TikTok"""
        if not url:
            return False
        
        for pattern in URLValidator.TIKTOK_PATTERNS:
            if re.search(pattern, url, re.IGNORECASE):
                return True
        return False
    
    @staticmethod
    def extract_youtube_id(url):
        """Extract YouTube video ID from URL"""
        if not url:
            return None
        
        for pattern in URLValidator.YOUTUBE_PATTERNS:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    @staticmethod
    def extract_tiktok_id(url):
        """Extract TikTok video ID from URL"""
        if not url:
            return None
        
        for pattern in URLValidator.TIKTOK_PATTERNS:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    @staticmethod
    def get_video_platform(url):
        """Determine video platform from URL"""
        if URLValidator.is_youtube_url(url):
            return 'youtube'
        elif URLValidator.is_tiktok_url(url):
            return 'tiktok'
        else:
            return None
    
    @staticmethod
    def normalize_url(url):
        """Normalize URL format"""
        if not url:
            return url
        
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        return url
    
    @staticmethod
    def is_downloadable_url(url, timeout=10):
        """Check if URL points to a downloadable file"""
        if not URLValidator.is_valid_url(url):
            return False
        
        try:
            response = requests.head(url, timeout=timeout, allow_redirects=True)
            
            # Check status code
            if response.status_code not in [200, 206]:
                return False
            
            # Check content type
            content_type = response.headers.get('Content-Type', '').lower()
            
            # Skip HTML pages (likely not direct file downloads)
            if 'text/html' in content_type:
                return False
            
            # Check if it has content length (file size)
            content_length = response.headers.get('Content-Length')
            if content_length and int(content_length) > 0:
                return True
            
            # If no content length, it might still be downloadable
            return True
            
        except:
            return False
    
    @staticmethod
    def get_url_info(url, timeout=10):
        """Get information about URL"""
        info = {
            'url': url,
            'valid': False,
            'downloadable': False,
            'platform': None,
            'content_type': None,
            'content_length': None,
            'filename': None,
            'supports_resume': False
        }
        
        if not URLValidator.is_valid_url(url):
            return info
        
        info['valid'] = True
        info['platform'] = URLValidator.get_video_platform(url)
        
        try:
            response = requests.head(url, timeout=timeout, allow_redirects=True)
            
            if response.status_code in [200, 206]:
                info['downloadable'] = True
                info['content_type'] = response.headers.get('Content-Type')
                
                content_length = response.headers.get('Content-Length')
                if content_length:
                    info['content_length'] = int(content_length)
                
                # Check if server supports range requests (resume)
                accept_ranges = response.headers.get('Accept-Ranges', '').lower()
                info['supports_resume'] = accept_ranges == 'bytes'
                
                # Try to get filename from headers
                content_disposition = response.headers.get('Content-Disposition')
                if content_disposition:
                    filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
                    if filename_match:
                        filename = filename_match.group(1).strip('"\'')
                        info['filename'] = filename
        
        except:
            pass
        
        return info
    
    @staticmethod
    def is_secure_url(url):
        """Check if URL uses HTTPS"""
        if not url:
            return False
        
        parsed = urlparse(url)
        return parsed.scheme == 'https'
    
    @staticmethod
    def get_domain(url):
        """Extract domain from URL"""
        if not url:
            return None
        
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower()
        except:
            return None
    
    @staticmethod
    def is_same_domain(url1, url2):
        """Check if two URLs are from the same domain"""
        domain1 = URLValidator.get_domain(url1)
        domain2 = URLValidator.get_domain(url2)
        
        return domain1 and domain2 and domain1 == domain2
    
    @staticmethod
    def clean_url(url):
        """Clean URL by removing tracking parameters"""
        if not url:
            return url
        
        try:
            parsed = urlparse(url)
            
            # Common tracking parameters to remove
            tracking_params = [
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'fbclid', 'gclid', 'ref', 'source', 'campaign'
            ]
            
            # Parse query parameters
            from urllib.parse import parse_qs, urlencode
            query_params = parse_qs(parsed.query)
            
            # Remove tracking parameters
            cleaned_params = {k: v for k, v in query_params.items() 
                            if k.lower() not in tracking_params}
            
            # Rebuild URL
            cleaned_query = urlencode(cleaned_params, doseq=True)
            cleaned_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            
            if cleaned_query:
                cleaned_url += f"?{cleaned_query}"
            
            if parsed.fragment:
                cleaned_url += f"#{parsed.fragment}"
            
            return cleaned_url
            
        except:
            return url
