"""
Download Manager - Core download engine with multi-threading support
"""

import threading
import queue
import time
import requests
import os
from pathlib import Path
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.downloader import Downloader
from src.core.database import DatabaseManager
from src.core.logger import get_logger

class DownloadManager:
    def __init__(self, scheduler=None, gui_parent=None):
        self.downloads = {}  # Active downloads
        self.download_queue = queue.Queue()
        self.max_concurrent = config.MAX_CONCURRENT_DOWNLOADS
        self.executor = None
        self.running = False
        self.db_manager = DatabaseManager()
        self.logger = get_logger(__name__)
        self.scheduler = scheduler
        self.gui_parent = gui_parent

        # Set scheduler reference
        if self.scheduler:
            self.scheduler.download_manager = self

        # Load settings
        self.load_settings()
    
    def load_settings(self):
        """Load settings from file"""
        settings_file = config.CONFIG_DIR / "settings.json"
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.max_concurrent = settings.get('max_concurrent_downloads', config.MAX_CONCURRENT_DOWNLOADS)
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
    
    def start(self):
        """Start the download manager"""
        if not self.running:
            self.running = True
            self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent)
            self.logger.info("Download manager started")
    
    def stop(self):
        """Stop the download manager"""
        if self.running:
            self.running = False

            # Stop all active downloads immediately
            self.logger.info("Stopping all active downloads...")
            for download_id in list(self.downloads.keys()):
                try:
                    download = self.downloads[download_id]
                    downloader = download['downloader']

                    # Stop the downloader immediately
                    downloader.stop()

                    # Cancel the future if it's running
                    if download['future'] and not download['future'].done():
                        download['future'].cancel()

                except Exception as e:
                    self.logger.error(f"Error stopping download {download_id}: {e}")

            # Force shutdown executor without waiting
            if self.executor:
                self.logger.info("Shutting down executor...")
                # Try graceful shutdown first
                self.executor.shutdown(wait=False)

                # Force shutdown after short timeout
                import threading
                def force_shutdown():
                    import time
                    time.sleep(2)  # Wait 2 seconds
                    try:
                        # Force terminate any remaining threads
                        for thread in threading.enumerate():
                            if thread != threading.current_thread() and 'ThreadPoolExecutor' in str(thread):
                                thread._stop()
                    except:
                        pass

                shutdown_thread = threading.Thread(target=force_shutdown, daemon=True)
                shutdown_thread.start()

            self.logger.info("Download manager stopped")
    
    def _check_duplicate_download(self, download_info):
        """Check if download already exists in queue"""
        url = download_info.get('url', '').strip()
        filename = download_info.get('filename', '').strip()
        save_path = download_info.get('save_path', '').strip()

        for existing_id, existing_download in self.downloads.items():
            existing_info = existing_download['info']
            existing_url = existing_info.get('url', '').strip()
            existing_filename = existing_info.get('filename', '').strip()
            existing_save_path = existing_info.get('save_path', '').strip()

            # Check for same URL (exact match)
            if url and existing_url and url == existing_url:
                return existing_id, 'url'

            # Check for same filename in same directory (exact match)
            if (filename and existing_filename and
                filename == existing_filename and
                save_path and existing_save_path and
                save_path == existing_save_path):
                return existing_id, 'filename'

        return None, None

    def add_download(self, download_info, progress_callback=None):
        """Add a new download"""
        download_id = download_info['id']

        if download_id in self.downloads:
            self.logger.warning(f"Download {download_id} already exists")
            return False

        # Check for duplicate downloads
        duplicate_id, duplicate_type = self._check_duplicate_download(download_info)
        if duplicate_id:
            existing_info = self.downloads[duplicate_id]['info']
            if duplicate_type == 'url':
                self.logger.warning(f"Download with same URL already in queue: {download_info['url']}")
                self.logger.info(f"Existing download: {existing_info['filename']}")
            elif duplicate_type == 'filename':
                self.logger.warning(f"Download with same filename already in queue: {download_info['filename']}")
                self.logger.info(f"Existing download ID: {duplicate_id}")
            return False
        
        try:
            # Create downloader instance
            downloader = Downloader(download_info, progress_callback)

            # Set GUI parent for duplicate file dialogs
            if self.gui_parent:
                downloader.set_gui_parent(self.gui_parent)

            # Store download
            self.downloads[download_id] = {
                'downloader': downloader,
                'future': None,
                'info': download_info,
                'callback': progress_callback
            }
            
            # Save to database
            self.db_manager.add_download(download_info)
            
            # Start download if manager is running
            if self.running:
                self._start_download(download_id)
            
            self.logger.info(f"Added download: {download_info['filename']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding download: {e}")
            return False
    
    def _start_download(self, download_id):
        """Start a specific download"""
        if download_id not in self.downloads:
            return False
        
        download = self.downloads[download_id]
        
        if download['future'] is not None and not download['future'].done():
            self.logger.warning(f"Download {download_id} is already running")
            return False
        
        try:
            # Submit download task to executor
            future = self.executor.submit(self._download_worker, download_id)
            download['future'] = future
            
            # Update status
            download['info']['status'] = 'downloading'
            self.db_manager.update_download_status(download_id, 'downloading')
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting download {download_id}: {e}")
            return False
    
    def _download_worker(self, download_id):
        """Worker function for downloading"""
        try:
            download = self.downloads[download_id]
            downloader = download['downloader']
            
            # Start the download
            success = downloader.start()
            
            if success:
                download['info']['status'] = 'completed'
                self.db_manager.update_download_status(download_id, 'completed')
                self.logger.info(f"Download completed: {download['info']['filename']}")
            else:
                download['info']['status'] = 'error'
                self.db_manager.update_download_status(download_id, 'error')
                self.logger.error(f"Download failed: {download['info']['filename']}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Download worker error for {download_id}: {e}")
            if download_id in self.downloads:
                self.downloads[download_id]['info']['status'] = 'error'
                self.db_manager.update_download_status(download_id, 'error')
            return False
    
    def pause_download(self, download_id):
        """Pause a download"""
        if download_id not in self.downloads:
            return False
        
        try:
            download = self.downloads[download_id]
            downloader = download['downloader']
            
            # Pause the downloader
            downloader.pause()
            
            # Update status
            download['info']['status'] = 'paused'
            self.db_manager.update_download_status(download_id, 'paused')
            
            self.logger.info(f"Download paused: {download['info']['filename']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error pausing download {download_id}: {e}")
            return False
    
    def resume_download(self, download_id):
        """Resume a paused download"""
        if download_id not in self.downloads:
            return False
        
        try:
            download = self.downloads[download_id]
            
            if download['info']['status'] != 'paused':
                return False
            
            # Resume by starting again
            return self._start_download(download_id)
            
        except Exception as e:
            self.logger.error(f"Error resuming download {download_id}: {e}")
            return False
    
    def stop_download(self, download_id):
        """Stop a download"""
        if download_id not in self.downloads:
            return False
        
        try:
            download = self.downloads[download_id]
            downloader = download['downloader']
            
            # Stop the downloader
            downloader.stop()
            
            # Cancel future if running
            if download['future'] and not download['future'].done():
                download['future'].cancel()
            
            # Update status
            download['info']['status'] = 'stopped'
            download['info']['progress'] = 0
            download['info']['downloaded'] = 0
            
            self.db_manager.update_download_status(download_id, 'stopped')
            
            self.logger.info(f"Download stopped: {download['info']['filename']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping download {download_id}: {e}")
            return False
    
    def remove_download(self, download_id):
        """Remove a download from manager"""
        if download_id not in self.downloads:
            return False
        
        try:
            # Stop download first
            self.stop_download(download_id)
            
            # Remove from downloads
            del self.downloads[download_id]
            
            # Remove from database
            self.db_manager.remove_download(download_id)
            
            self.logger.info(f"Download removed: {download_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error removing download {download_id}: {e}")
            return False
    
    def get_download_info(self, download_id):
        """Get download information"""
        if download_id in self.downloads:
            return self.downloads[download_id]['info']
        return None
    
    def get_all_downloads(self):
        """Get all downloads information"""
        return {download_id: download['info'] for download_id, download in self.downloads.items()}
    
    def get_active_downloads_count(self):
        """Get count of active downloads"""
        count = 0
        for download in self.downloads.values():
            if download['info']['status'] == 'downloading':
                count += 1
        return count
    
    def get_total_speed(self):
        """Get total download speed"""
        total_speed = 0
        for download in self.downloads.values():
            if download['info']['status'] == 'downloading':
                total_speed += download['info'].get('speed', 0)
        return total_speed
    
    def load_downloads_from_db(self):
        """Load incomplete downloads from database"""
        try:
            downloads = self.db_manager.get_incomplete_downloads()
            
            for download_info in downloads:
                # Reset status to pending
                download_info['status'] = 'pending'
                download_info['progress'] = 0
                download_info['speed'] = 0
                
                # Create downloader but don't start yet
                downloader = Downloader(download_info)
                
                self.downloads[download_info['id']] = {
                    'downloader': downloader,
                    'future': None,
                    'info': download_info,
                    'callback': None
                }
            
            self.logger.info(f"Loaded {len(downloads)} downloads from database")
            
        except Exception as e:
            self.logger.error(f"Error loading downloads from database: {e}")
    
    def cleanup_completed_downloads(self):
        """Remove completed downloads from memory (keep in database)"""
        completed_ids = []
        
        for download_id, download in self.downloads.items():
            if download['info']['status'] == 'completed':
                completed_ids.append(download_id)
        
        for download_id in completed_ids:
            del self.downloads[download_id]
        
        if completed_ids:
            self.logger.info(f"Cleaned up {len(completed_ids)} completed downloads")
    
    def get_statistics(self):
        """Get download statistics"""
        stats = {
            'total_downloads': len(self.downloads),
            'active_downloads': 0,
            'completed_downloads': 0,
            'paused_downloads': 0,
            'failed_downloads': 0,
            'total_speed': 0,
            'total_downloaded': 0,
            'total_size': 0
        }
        
        for download in self.downloads.values():
            status = download['info']['status']
            
            if status == 'downloading':
                stats['active_downloads'] += 1
                stats['total_speed'] += download['info'].get('speed', 0)
            elif status == 'completed':
                stats['completed_downloads'] += 1
            elif status == 'paused':
                stats['paused_downloads'] += 1
            elif status == 'error':
                stats['failed_downloads'] += 1
            
            stats['total_downloaded'] += download['info'].get('downloaded', 0)
            stats['total_size'] += download['info'].get('size', 0)
        
        return stats
