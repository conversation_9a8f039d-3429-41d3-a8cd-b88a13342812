"""
Core Downloader Class - Handles individual file downloads with resume support
"""

import requests
import os
import time
import threading
from pathlib import Path
import sys
from urllib.parse import urlparse
import hashlib

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger
from src.downloaders.youtube_downloader import YouTubeDownloader
from src.downloaders.tiktok_downloader import TikTokDownloader
from src.utils.url_validator import URLValidator

class Downloader:
    def __init__(self, download_info, progress_callback=None):
        self.download_info = download_info
        self.progress_callback = progress_callback
        self.logger = get_logger(__name__)
        
        self.url = download_info['url']
        self.filename = download_info['filename']
        self.save_path = download_info['save_path']
        self.full_path = Path(self.save_path) / self.filename
        
        # Download state
        self.is_paused = False
        self.is_stopped = False
        self.is_downloading = False
        
        # Progress tracking
        self.total_size = 0
        self.downloaded_size = 0
        self.start_time = 0
        self.last_update_time = 0
        self.current_speed = 0
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config.USER_AGENT
        })
        
        # Load settings
        self.load_settings()
    
    def load_settings(self):
        """Load download settings"""
        import json
        settings_file = config.CONFIG_DIR / "settings.json"
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.chunk_size = settings.get('chunk_size', config.DEFAULT_CHUNK_SIZE)
                    self.timeout = settings.get('timeout', config.DEFAULT_TIMEOUT)
                    self.max_retries = settings.get('max_retries', config.MAX_RETRIES)
                    
                    # Proxy settings
                    if settings.get('use_proxy', False):
                        proxy_host = settings.get('proxy_host', '')
                        proxy_port = settings.get('proxy_port', '')
                        if proxy_host and proxy_port:
                            proxy_url = f"http://{proxy_host}:{proxy_port}"
                            self.session.proxies = {
                                'http': proxy_url,
                                'https': proxy_url
                            }
            else:
                self.chunk_size = config.DEFAULT_CHUNK_SIZE
                self.timeout = config.DEFAULT_TIMEOUT
                self.max_retries = config.MAX_RETRIES
                
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
            self.chunk_size = config.DEFAULT_CHUNK_SIZE
            self.timeout = config.DEFAULT_TIMEOUT
            self.max_retries = config.MAX_RETRIES
    
    def start(self):
        """Start the download"""
        if self.is_downloading:
            return False

        try:
            self.is_downloading = True
            self.is_paused = False
            self.is_stopped = False
            self.start_time = time.time()

            # Check if this is a video download (YouTube/TikTok)
            if self._is_video_url():
                success = self._download_video()
                self.is_downloading = False
                return success

            # Regular file download
            # Check if file already exists and get resume position
            resume_pos = 0
            if self.full_path.exists():
                resume_pos = self.full_path.stat().st_size
                self.downloaded_size = resume_pos

            # Get file info
            if not self._get_file_info():
                self.is_downloading = False
                return False

            # Start download
            success = self._download_file(resume_pos)

            self.is_downloading = False
            return success
            
        except Exception as e:
            self.logger.error(f"Download error: {e}")
            self.is_downloading = False
            return False

    def _is_video_url(self):
        """Check if URL is a video URL (YouTube/TikTok)"""
        return (URLValidator.is_youtube_url(self.url) or
                URLValidator.is_tiktok_url(self.url) or
                self.download_info.get('video_type') in ['youtube', 'tiktok'])

    def _download_video(self):
        """Download video using appropriate downloader"""
        try:
            video_type = self.download_info.get('video_type')
            quality = self.download_info.get('quality', 'best')

            # Determine video type if not specified
            if not video_type:
                if URLValidator.is_youtube_url(self.url):
                    video_type = 'youtube'
                elif URLValidator.is_tiktok_url(self.url):
                    video_type = 'tiktok'
                else:
                    self.logger.error("Unknown video type")
                    return False

            # Create appropriate downloader
            if video_type == 'youtube':
                downloader = YouTubeDownloader(progress_callback=self._video_progress_callback)
                return downloader.download_video(
                    self.url,
                    self.save_path,
                    quality=quality
                )
            elif video_type == 'tiktok':
                downloader = TikTokDownloader(progress_callback=self._video_progress_callback)
                return downloader.download_video(
                    self.url,
                    self.save_path,
                    quality=quality
                )
            else:
                self.logger.error(f"Unsupported video type: {video_type}")
                return False

        except Exception as e:
            self.logger.error(f"Video download error: {e}")
            return False

    def _video_progress_callback(self, progress_info):
        """Handle progress updates from video downloaders"""
        if self.progress_callback:
            # Convert video downloader progress to standard format
            status = progress_info.get('status', 'downloading')

            if status == 'downloading':
                downloaded = progress_info.get('downloaded_bytes', 0)
                total = progress_info.get('total_bytes', 0)

                self.downloaded_size = downloaded
                self.total_size = total

                # Calculate speed
                current_time = time.time()
                if hasattr(self, 'last_update_time') and self.last_update_time > 0:
                    time_diff = current_time - self.last_update_time
                    if time_diff > 0:
                        self.current_speed = (downloaded - getattr(self, 'last_downloaded', 0)) / time_diff

                self.last_update_time = current_time
                self.last_downloaded = downloaded

                # Call main progress callback
                self.progress_callback({
                    'id': self.download_info['id'],
                    'status': 'downloading',
                    'progress': (downloaded / total * 100) if total > 0 else 0,
                    'downloaded': downloaded,
                    'total': total,
                    'speed': self.current_speed,
                    'filename': progress_info.get('filename', self.filename)
                })
            elif status == 'finished':
                if self.progress_callback:
                    self.progress_callback({
                        'id': self.download_info['id'],
                        'status': 'completed',
                        'progress': 100,
                        'downloaded': self.total_size,
                        'total': self.total_size,
                        'speed': 0,
                        'filename': self.filename
                    })
    
    def _get_file_info(self):
        """Get file information from server"""
        try:
            response = self.session.head(self.url, timeout=self.timeout, allow_redirects=True)
            
            if response.status_code == 200:
                # Get file size
                content_length = response.headers.get('Content-Length')
                if content_length:
                    self.total_size = int(content_length)
                    self.download_info['size'] = self.total_size
                
                # Check if server supports range requests
                accept_ranges = response.headers.get('Accept-Ranges', '').lower()
                self.supports_resume = accept_ranges == 'bytes'
                
                return True
            else:
                self.logger.error(f"Failed to get file info: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error getting file info: {e}")
            return False
    
    def _download_file(self, resume_pos=0):
        """Download the file with resume support"""
        headers = {}
        
        # Add range header for resume
        if resume_pos > 0 and self.supports_resume:
            headers['Range'] = f'bytes={resume_pos}-'
        
        retries = 0
        while retries < self.max_retries:
            try:
                response = self.session.get(
                    self.url,
                    headers=headers,
                    stream=True,
                    timeout=self.timeout
                )
                
                if response.status_code in [200, 206]:  # 200 = OK, 206 = Partial Content
                    return self._write_file(response, resume_pos)
                else:
                    self.logger.error(f"HTTP Error: {response.status_code}")
                    retries += 1
                    time.sleep(config.RETRY_DELAY)
                    
            except Exception as e:
                self.logger.error(f"Download attempt {retries + 1} failed: {e}")
                retries += 1
                if retries < self.max_retries:
                    time.sleep(config.RETRY_DELAY)
        
        return False
    
    def _write_file(self, response, resume_pos):
        """Write downloaded data to file"""
        try:
            # Create directory if it doesn't exist
            self.full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Open file in append mode if resuming, otherwise write mode
            mode = 'ab' if resume_pos > 0 else 'wb'
            
            with open(self.full_path, mode) as file:
                last_update = time.time()
                bytes_since_update = 0
                
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if self.is_stopped:
                        return False
                    
                    # Pause handling
                    while self.is_paused and not self.is_stopped:
                        time.sleep(0.1)
                    
                    if chunk:  # Filter out keep-alive chunks
                        file.write(chunk)
                        chunk_size = len(chunk)
                        self.downloaded_size += chunk_size
                        bytes_since_update += chunk_size
                        
                        # Update progress periodically
                        current_time = time.time()
                        if current_time - last_update >= 0.5:  # Update every 0.5 seconds
                            self._update_progress(bytes_since_update, current_time - last_update)
                            last_update = current_time
                            bytes_since_update = 0
                
                # Final progress update
                self._update_progress(0, 0)
                
            return True
            
        except Exception as e:
            self.logger.error(f"Error writing file: {e}")
            return False
    
    def _update_progress(self, bytes_downloaded, time_elapsed):
        """Update download progress"""
        try:
            # Calculate speed
            if time_elapsed > 0:
                self.current_speed = bytes_downloaded / time_elapsed
            
            # Calculate progress percentage
            if self.total_size > 0:
                progress = (self.downloaded_size / self.total_size) * 100
            else:
                progress = 0
            
            # Update download info
            self.download_info.update({
                'progress': progress,
                'speed': self.current_speed,
                'downloaded': self.downloaded_size,
                'size': self.total_size
            })
            
            # Call progress callback
            if self.progress_callback:
                self.progress_callback(self.download_info.copy())
                
        except Exception as e:
            self.logger.error(f"Error updating progress: {e}")
    
    def pause(self):
        """Pause the download"""
        self.is_paused = True
        self.logger.info(f"Download paused: {self.filename}")
    
    def resume(self):
        """Resume the download"""
        self.is_paused = False
        self.logger.info(f"Download resumed: {self.filename}")
    
    def stop(self):
        """Stop the download"""
        self.is_stopped = True
        self.is_paused = False
        self.logger.info(f"Download stopped: {self.filename}")
    
    def get_progress(self):
        """Get current download progress"""
        if self.total_size > 0:
            return (self.downloaded_size / self.total_size) * 100
        return 0
    
    def get_speed(self):
        """Get current download speed"""
        return self.current_speed
    
    def get_eta(self):
        """Get estimated time of arrival"""
        if self.current_speed > 0 and self.total_size > 0:
            remaining_bytes = self.total_size - self.downloaded_size
            return remaining_bytes / self.current_speed
        return 0
    
    def is_complete(self):
        """Check if download is complete"""
        return self.downloaded_size >= self.total_size and self.total_size > 0
    
    def verify_download(self):
        """Verify downloaded file integrity"""
        if not self.full_path.exists():
            return False
        
        # Check file size
        actual_size = self.full_path.stat().st_size
        if self.total_size > 0 and actual_size != self.total_size:
            self.logger.warning(f"File size mismatch: expected {self.total_size}, got {actual_size}")
            return False
        
        return True
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                self.session.close()
        except:
            pass
