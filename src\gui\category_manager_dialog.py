"""
Category Manager Dialog for Python Download Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import customtkinter as ctk
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.category_manager import CategoryManager, CategoryRule

class CategoryManagerDialog:
    def __init__(self, parent, category_manager):
        self.parent = parent
        self.category_manager = category_manager
        
        # Create dialog window
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("إدارة التصنيفات")
        self.dialog.geometry("900x700")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.create_widgets()
        self.refresh_categories()
        self.refresh_rules()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Create notebook for tabs
        self.notebook = ctk.CTkTabview(self.dialog)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Categories tab
        self.create_categories_tab()
        
        # Rules tab
        self.create_rules_tab()
        
        # Buttons frame
        button_frame = ctk.CTkFrame(self.dialog)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Close button
        self.close_btn = ctk.CTkButton(
            button_frame,
            text="إغلاق",
            command=self.close_dialog,
            width=100
        )
        self.close_btn.pack(side="right", padx=15, pady=15)
        
        # Apply button
        self.apply_btn = ctk.CTkButton(
            button_frame,
            text="تطبيق",
            command=self.apply_changes,
            width=100
        )
        self.apply_btn.pack(side="right", padx=(15, 10), pady=15)
    
    def create_categories_tab(self):
        """Create categories management tab"""
        categories_tab = self.notebook.add("التصنيفات")
        
        # Toolbar
        toolbar_frame = ctk.CTkFrame(categories_tab)
        toolbar_frame.pack(fill="x", padx=15, pady=15)
        
        self.add_category_btn = ctk.CTkButton(
            toolbar_frame,
            text="إضافة تصنيف",
            command=self.add_category,
            width=120
        )
        self.add_category_btn.pack(side="left", padx=10, pady=10)
        
        self.edit_category_btn = ctk.CTkButton(
            toolbar_frame,
            text="تعديل",
            command=self.edit_category,
            width=80
        )
        self.edit_category_btn.pack(side="left", padx=5, pady=10)
        
        self.delete_category_btn = ctk.CTkButton(
            toolbar_frame,
            text="حذف",
            command=self.delete_category,
            width=80
        )
        self.delete_category_btn.pack(side="left", padx=5, pady=10)
        
        # Categories list
        list_frame = ctk.CTkFrame(categories_tab)
        list_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # Create treeview for categories
        self.categories_tree_frame = ctk.CTkFrame(list_frame)
        self.categories_tree_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        columns = ("name", "description", "extensions", "path")
        self.categories_tree = ttk.Treeview(self.categories_tree_frame, columns=columns, show="headings", height=12)
        
        # Define headings
        self.categories_tree.heading("name", text="الاسم")
        self.categories_tree.heading("description", text="الوصف")
        self.categories_tree.heading("extensions", text="الامتدادات")
        self.categories_tree.heading("path", text="المسار")
        
        # Configure column widths
        self.categories_tree.column("name", width=120)
        self.categories_tree.column("description", width=200)
        self.categories_tree.column("extensions", width=200)
        self.categories_tree.column("path", width=150)
        
        # Add scrollbar
        categories_scrollbar = ttk.Scrollbar(self.categories_tree_frame, orient="vertical", command=self.categories_tree.yview)
        self.categories_tree.configure(yscrollcommand=categories_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.categories_tree.pack(side="left", fill="both", expand=True)
        categories_scrollbar.pack(side="right", fill="y")
    
    def create_rules_tab(self):
        """Create rules management tab"""
        rules_tab = self.notebook.add("القواعد")
        
        # Toolbar
        toolbar_frame = ctk.CTkFrame(rules_tab)
        toolbar_frame.pack(fill="x", padx=15, pady=15)
        
        self.add_rule_btn = ctk.CTkButton(
            toolbar_frame,
            text="إضافة قاعدة",
            command=self.add_rule,
            width=120
        )
        self.add_rule_btn.pack(side="left", padx=10, pady=10)
        
        self.edit_rule_btn = ctk.CTkButton(
            toolbar_frame,
            text="تعديل",
            command=self.edit_rule,
            width=80
        )
        self.edit_rule_btn.pack(side="left", padx=5, pady=10)
        
        self.delete_rule_btn = ctk.CTkButton(
            toolbar_frame,
            text="حذف",
            command=self.delete_rule,
            width=80
        )
        self.delete_rule_btn.pack(side="left", padx=5, pady=10)
        
        self.toggle_rule_btn = ctk.CTkButton(
            toolbar_frame,
            text="تفعيل/إلغاء",
            command=self.toggle_rule,
            width=100
        )
        self.toggle_rule_btn.pack(side="left", padx=5, pady=10)
        
        # Rules list
        list_frame = ctk.CTkFrame(rules_tab)
        list_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # Create treeview for rules
        self.rules_tree_frame = ctk.CTkFrame(list_frame)
        self.rules_tree_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        columns = ("name", "type", "pattern", "category", "priority", "active")
        self.rules_tree = ttk.Treeview(self.rules_tree_frame, columns=columns, show="headings", height=12)
        
        # Define headings
        self.rules_tree.heading("name", text="الاسم")
        self.rules_tree.heading("type", text="النوع")
        self.rules_tree.heading("pattern", text="النمط")
        self.rules_tree.heading("category", text="التصنيف")
        self.rules_tree.heading("priority", text="الأولوية")
        self.rules_tree.heading("active", text="نشط")
        
        # Configure column widths
        self.rules_tree.column("name", width=120)
        self.rules_tree.column("type", width=100)
        self.rules_tree.column("pattern", width=150)
        self.rules_tree.column("category", width=100)
        self.rules_tree.column("priority", width=80)
        self.rules_tree.column("active", width=60)
        
        # Add scrollbar
        rules_scrollbar = ttk.Scrollbar(self.rules_tree_frame, orient="vertical", command=self.rules_tree.yview)
        self.rules_tree.configure(yscrollcommand=rules_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.rules_tree.pack(side="left", fill="both", expand=True)
        rules_scrollbar.pack(side="right", fill="y")
    
    def refresh_categories(self):
        """Refresh categories list"""
        # Clear existing items
        for item in self.categories_tree.get_children():
            self.categories_tree.delete(item)
        
        # Get categories
        categories = self.category_manager.get_categories()
        
        # Add items to tree
        for category_id, category_info in categories.items():
            name = category_info.get('name', category_id)
            description = category_info.get('description', '')
            extensions = ', '.join(category_info.get('extensions', [])[:5])  # Show first 5
            if len(category_info.get('extensions', [])) > 5:
                extensions += '...'
            path = category_info.get('default_path', '')
            
            self.categories_tree.insert("", "end", values=(name, description, extensions, path))
    
    def refresh_rules(self):
        """Refresh rules list"""
        # Clear existing items
        for item in self.rules_tree.get_children():
            self.rules_tree.delete(item)
        
        # Get rules
        rules = self.category_manager.get_rules()
        
        # Add items to tree
        for rule in rules:
            type_map = {
                "extension": "امتداد",
                "url_domain": "نطاق URL",
                "url_pattern": "نمط URL",
                "filename_pattern": "نمط اسم الملف",
                "size": "حجم الملف"
            }
            
            rule_type = type_map.get(rule.rule_type, rule.rule_type)
            active = "نعم" if rule.is_active else "لا"
            
            self.rules_tree.insert("", "end", values=(
                rule.name, rule_type, rule.pattern, rule.target_category, rule.priority, active
            ))
    
    def add_category(self):
        """Add new category"""
        dialog = CategoryEditDialog(self.dialog, None, self.category_manager)
        if dialog.result:
            self.refresh_categories()
    
    def edit_category(self):
        """Edit selected category"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تصنيف للتعديل")
            return
        
        # Get category ID from selection
        item_values = self.categories_tree.item(selection[0])['values']
        category_name = item_values[0]
        
        # Find category by name
        categories = self.category_manager.get_categories()
        category_id = None
        for cid, cinfo in categories.items():
            if cinfo.get('name', cid) == category_name:
                category_id = cid
                break
        
        if category_id:
            dialog = CategoryEditDialog(self.dialog, category_id, self.category_manager)
            if dialog.result:
                self.refresh_categories()
    
    def delete_category(self):
        """Delete selected category"""
        selection = self.categories_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تصنيف للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل تريد حذف هذا التصنيف؟"):
            # Implementation for deleting category
            messagebox.showinfo("معلومات", "ميزة الحذف ستكون متاحة قريباً")
    
    def add_rule(self):
        """Add new rule"""
        dialog = RuleEditDialog(self.dialog, None, self.category_manager)
        if dialog.result:
            self.refresh_rules()
    
    def edit_rule(self):
        """Edit selected rule"""
        selection = self.rules_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قاعدة للتعديل")
            return
        
        # Get rule name from selection
        item_values = self.rules_tree.item(selection[0])['values']
        rule_name = item_values[0]
        
        dialog = RuleEditDialog(self.dialog, rule_name, self.category_manager)
        if dialog.result:
            self.refresh_rules()
    
    def delete_rule(self):
        """Delete selected rule"""
        selection = self.rules_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قاعدة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل تريد حذف هذه القاعدة؟"):
            item_values = self.rules_tree.item(selection[0])['values']
            rule_name = item_values[0]
            
            if self.category_manager.remove_rule(rule_name):
                messagebox.showinfo("نجح", "تم حذف القاعدة بنجاح")
                self.refresh_rules()
            else:
                messagebox.showerror("خطأ", "فشل في حذف القاعدة")
    
    def toggle_rule(self):
        """Toggle rule active state"""
        selection = self.rules_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قاعدة")
            return
        
        # Implementation for toggling rule
        messagebox.showinfo("معلومات", "ميزة التفعيل/الإلغاء ستكون متاحة قريباً")
    
    def apply_changes(self):
        """Apply changes"""
        self.category_manager.save_categories()
        self.category_manager.save_rules()
        messagebox.showinfo("نجح", "تم حفظ التغييرات بنجاح")
    
    def close_dialog(self):
        """Close dialog"""
        self.dialog.destroy()

class CategoryEditDialog:
    def __init__(self, parent, category_id, category_manager):
        self.parent = parent
        self.category_id = category_id
        self.category_manager = category_manager
        self.result = False
        
        # Create dialog
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("تعديل التصنيف" if category_id else "إضافة تصنيف")
        self.dialog.geometry("400x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
        if category_id:
            self.load_category_data()
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Implementation for category edit dialog
        messagebox.showinfo("معلومات", "نافذة تعديل التصنيف ستكون متاحة قريباً")
        self.dialog.destroy()
    
    def load_category_data(self):
        """Load category data for editing"""
        pass

class RuleEditDialog:
    def __init__(self, parent, rule_name, category_manager):
        self.parent = parent
        self.rule_name = rule_name
        self.category_manager = category_manager
        self.result = False
        
        # Create dialog
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("تعديل القاعدة" if rule_name else "إضافة قاعدة")
        self.dialog.geometry("400x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_widgets()
        
        if rule_name:
            self.load_rule_data()
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Implementation for rule edit dialog
        messagebox.showinfo("معلومات", "نافذة تعديل القاعدة ستكون متاحة قريباً")
        self.dialog.destroy()
    
    def load_rule_data(self):
        """Load rule data for editing"""
        pass
