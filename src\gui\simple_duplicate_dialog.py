"""
Simple Duplicate File Dialog - Lightweight version for threading
"""

import tkinter as tk
from tkinter import messagebox
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.file_utils import get_file_size, format_file_size

def show_simple_duplicate_dialog(existing_file_path, new_filename):
    """Show simple duplicate file dialog"""
    try:
        existing_path = Path(existing_file_path)
        
        # Create message
        message = f"الملف موجود مسبقاً:\n\n"
        message += f"📁 الملف الموجود: {existing_path.name}\n"
        
        if existing_path.exists():
            size = get_file_size(str(existing_path))
            message += f"📊 الحجم: {format_file_size(size)}\n"
        
        message += f"\n📥 الملف الجديد: {new_filename}\n\n"
        message += "ماذا تريد أن تفعل؟"
        
        # Create root window (hidden)
        root = tk.Tk()
        root.withdraw()
        
        # Show dialog with options
        result = messagebox.askyesnocancel(
            "ملف موجود مسبقاً",
            message + "\n\n" +
            "نعم = استبدال الملف الموجود\n" +
            "لا = إعادة تسمية تلقائية\n" +
            "إلغاء = تخطي التحميل",
            icon='warning'
        )
        
        root.destroy()
        
        if result is True:
            return "replace"
        elif result is False:
            return "rename"
        else:
            return "skip"
            
    except Exception as e:
        print(f"Error in duplicate dialog: {e}")
        return "rename"  # Fallback

def show_duplicate_choice(existing_file_path, new_filename):
    """Show duplicate file choice using simple dialog"""
    return show_simple_duplicate_dialog(existing_file_path, new_filename)
