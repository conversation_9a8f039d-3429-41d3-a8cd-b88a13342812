# دليل استخدام Python Download Manager

## 🎉 البرنامج يعمل الآن بكامل خصائصه!

### ✅ حالة النظام:
- ✅ البرنامج الرئيسي يعمل
- ✅ قاعدة البيانات مُفعلة
- ✅ مدير التصنيفات (13 قاعدة تصنيف)
- ✅ مدير التحميل متعدد الخيوط
- ✅ جدولة التحميل
- ✅ خادم إضافة المتصفح (المنفذ 9876)
- ✅ واجهة المستخدم الرسومية

## 📋 كيفية الاستخدام:

### 1. النافذة الرئيسية
- **العنوان**: "Python Download Manager"
- **الأزرار الرئيسية**:
  - `إضافة تحميل` - لإضافة تحميل جديد
  - `بدء الكل` - لبدء جميع التحميلات
  - `إيقاف الكل` - لإيقاف جميع التحميلات
  - `الإعدادات` - لفتح نافذة الإعدادات

### 2. إضافة تحميل جديد:
1. **انقر على "إضافة تحميل"**
2. **في نافذة "إضافة تحميل جديد"**:
   - الصق الرابط في حقل "رابط التحميل"
   - انقر "تحليل" لتحليل الرابط
   - اختر الجودة (للفيديوهات)
   - حدد مجلد الحفظ
   - اختر التصنيف
3. **انقر الزر الأحمر الكبير "🔽 إضافة للتحميل"**

### 3. أنواع الروابط المدعومة:

#### 🎬 YouTube:
```
https://www.youtube.com/watch?v=dQw4w9WgXcQ
https://youtu.be/dQw4w9WgXcQ
```

#### 📱 TikTok:
```
https://www.tiktok.com/@username/video/1234567890
https://vm.tiktok.com/ZMeAbCdEf/
```

#### 📁 ملفات مباشرة:
```
https://example.com/file.zip
https://example.com/document.pdf
https://example.com/image.jpg
```

### 4. الميزات المتقدمة:

#### 📅 الجدولة:
- يمكن جدولة التحميلات لأوقات محددة
- دعم التحميل المتكرر
- إيقاف/تشغيل تلقائي

#### 📂 التصنيف التلقائي:
- **Videos**: mp4, avi, mkv, mov
- **Music**: mp3, wav, flac, aac
- **Documents**: pdf, doc, txt, ppt
- **Images**: jpg, png, gif, bmp
- **Archives**: zip, rar, 7z, tar
- **Software**: exe, msi, deb, dmg
- **Others**: باقي الأنواع

#### ⚡ التحميل المتقدم:
- دعم الاستكمال
- تحميل متعدد الخيوط
- إدارة السرعة
- إحصائيات مفصلة

### 5. إضافة المتصفح:

#### تثبيت الإضافة:
1. **Chrome/Edge**:
   - اذهب إلى `chrome://extensions/`
   - فعل "وضع المطور"
   - انقر "تحميل إضافة غير مُعبأة"
   - اختر مجلد `browser_extension`

2. **Firefox**:
   - اذهب إلى `about:debugging`
   - انقر "This Firefox"
   - انقر "Load Temporary Add-on"
   - اختر ملف `manifest.json`

#### استخدام الإضافة:
- ستظهر أيقونة التحميل في شريط الأدوات
- انقر عليها في أي صفحة لتحميل الفيديو/الملف
- ستفتح نافذة منبثقة للتحكم

### 6. الإعدادات:

#### إعدادات التحميل:
- عدد التحميلات المتزامنة
- مجلد التحميل الافتراضي
- حجم الجزء (Chunk Size)
- مهلة الاتصال

#### إعدادات الفيديو:
- جودة YouTube الافتراضية
- جودة TikTok الافتراضية
- تحميل الترجمات
- تحويل الصوت

### 7. استكشاف الأخطاء:

#### إذا لم يعمل التحميل:
1. تحقق من اتصال الإنترنت
2. جرب رابط آخر
3. تحقق من صحة الرابط
4. راجع ملف السجل في `logs/`

#### إذا لم تظهر الأزرار:
1. أعد تشغيل البرنامج
2. تحقق من حجم النافذة
3. جرب تكبير النافذة

### 8. ملفات مهمة:

- `config/settings.json` - الإعدادات
- `logs/download_manager.log` - سجل الأحداث
- `downloads/` - مجلد التحميلات
- `config/downloads.db` - قاعدة البيانات

## 🚀 البرنامج جاهز للاستخدام!

**حالة الخدمات**:
- 🟢 البرنامج الرئيسي: يعمل
- 🟢 خادم المتصفح: http://localhost:9876
- 🟢 قاعدة البيانات: متصلة
- 🟢 مدير التحميل: نشط

**للدعم**: تحقق من ملفات السجل في مجلد `logs/`
