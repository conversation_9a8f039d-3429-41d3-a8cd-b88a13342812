#!/usr/bin/env python3
"""
Quick Start Script for Python Download Manager
For testing and development purposes
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'tkinter',
        'customtkinter', 
        'requests',
        'yt_dlp',
        'flask',
        'flask_cors',
        'Pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'customtkinter':
                import customtkinter
            elif package == 'requests':
                import requests
            elif package == 'yt_dlp':
                import yt_dlp
            elif package == 'flask':
                import flask
            elif package == 'flask_cors':
                import flask_cors
            elif package == 'Pillow':
                import PIL
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_missing_packages(packages):
    """Install missing packages"""
    import subprocess
    
    print("Installing missing packages...")
    for package in packages:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "downloads",
        "config",
        "logs", 
        "temp",
        "logs/downloads"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

def main():
    """Main function"""
    print("🚀 Python Download Manager - Quick Start")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or newer is required")
        print(f"Current version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check requirements
    missing = check_requirements()
    
    if missing:
        print(f"⚠️  Missing packages: {', '.join(missing)}")
        
        response = input("Do you want to install missing packages? (y/n): ")
        if response.lower() in ['y', 'yes']:
            if not install_missing_packages(missing):
                print("❌ Failed to install some packages")
                sys.exit(1)
        else:
            print("❌ Cannot continue without required packages")
            sys.exit(1)
    
    print("✅ All requirements satisfied")
    
    # Create directories
    create_directories()
    print("✅ Directories created")
    
    # Import and run main application
    try:
        print("\n🎯 Starting Python Download Manager...")
        print("=" * 50)
        
        # Import main module
        import main
        
        # Run the application
        main.main()
        
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
