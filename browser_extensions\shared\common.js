/**
 * ملف مشترك للوظائف المستخدمة في جميع المتصفحات
 * Python Download Manager - Browser Extensions
 */

// إعدادات مشتركة
const EXTENSION_CONFIG = {
    SERVER_URL: 'http://localhost:9876',
    TIMEOUT: 10000,
    CACHE_DURATION: 300000, // 5 minutes
    MAX_RETRIES: 3
};

// وظائف مساعدة مشتركة
const CommonUtils = {
    /**
     * تنسيق حجم الملف
     */
    formatFileSize: function(bytes) {
        if (!bytes || bytes === 0) return 'غير معروف';
        
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        const size = bytes / Math.pow(1024, i);
        
        return Math.round(size * 100) / 100 + ' ' + sizes[i];
    },

    /**
     * استخراج معرف الفيديو من رابط YouTube
     */
    extractVideoId: function(url) {
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(regex);
        return match ? match[1] : null;
    },

    /**
     * التحقق من صحة رابط YouTube
     */
    isYouTubeUrl: function(url) {
        return /^https?:\/\/(?:www\.)?(?:youtube\.com|youtu\.be)/.test(url);
    },

    /**
     * إنشاء معرف فريد
     */
    generateId: function() {
        return 'pdm_' + Math.random().toString(36).substr(2, 9);
    },

    /**
     * تأخير التنفيذ
     */
    delay: function(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * إنشاء عنصر HTML مع خصائص
     */
    createElement: function(tag, properties = {}, styles = {}) {
        const element = document.createElement(tag);
        
        // تطبيق الخصائص
        Object.keys(properties).forEach(key => {
            if (key === 'innerHTML') {
                element.innerHTML = properties[key];
            } else if (key === 'textContent') {
                element.textContent = properties[key];
            } else {
                element.setAttribute(key, properties[key]);
            }
        });
        
        // تطبيق الأنماط
        Object.keys(styles).forEach(key => {
            element.style[key] = styles[key];
        });
        
        return element;
    },

    /**
     * إزالة عنصر من DOM
     */
    removeElement: function(elementOrId) {
        const element = typeof elementOrId === 'string' 
            ? document.getElementById(elementOrId) 
            : elementOrId;
        
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    },

    /**
     * إضافة مستمع أحداث مع إزالة تلقائية
     */
    addEventListenerWithCleanup: function(element, event, handler, options = {}) {
        element.addEventListener(event, handler, options);
        
        // إرجاع دالة لإزالة المستمع
        return () => {
            element.removeEventListener(event, handler, options);
        };
    },

    /**
     * تشفير النص لاستخدامه في HTML
     */
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * التحقق من وجود عنصر في DOM
     */
    elementExists: function(selector) {
        return document.querySelector(selector) !== null;
    },

    /**
     * انتظار ظهور عنصر في DOM
     */
    waitForElement: function(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    },

    /**
     * نسخ النص إلى الحافظة
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            return navigator.clipboard.writeText(text);
        } else {
            // Fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            return new Promise((resolve, reject) => {
                try {
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    if (successful) {
                        resolve();
                    } else {
                        reject(new Error('Copy command failed'));
                    }
                } catch (err) {
                    document.body.removeChild(textArea);
                    reject(err);
                }
            });
        }
    },

    /**
     * تسجيل الأخطاء مع معلومات إضافية
     */
    logError: function(error, context = '') {
        const timestamp = new Date().toISOString();
        const message = `[PDM Extension] ${timestamp} - ${context}: ${error.message || error}`;
        console.error(message, error);
        
        // يمكن إضافة إرسال الأخطاء إلى خادم التسجيل هنا
    },

    /**
     * تسجيل المعلومات
     */
    logInfo: function(message, data = null) {
        const timestamp = new Date().toISOString();
        const logMessage = `[PDM Extension] ${timestamp} - ${message}`;
        console.log(logMessage, data || '');
    },

    /**
     * التحقق من حالة الاتصال
     */
    checkConnection: function() {
        return navigator.onLine;
    },

    /**
     * تنظيف البيانات المؤقتة القديمة
     */
    cleanupOldCache: function(storage, maxAge = 300000) {
        const now = Date.now();
        Object.keys(storage).forEach(key => {
            if (key.startsWith('pdm_cache_')) {
                const data = storage[key];
                if (data && data.timestamp && (now - data.timestamp) > maxAge) {
                    delete storage[key];
                }
            }
        });
    }
};

// تصدير للاستخدام في الوحدات الأخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EXTENSION_CONFIG, CommonUtils };
}
