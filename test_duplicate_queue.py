#!/usr/bin/env python3
"""
Test duplicate download detection in queue
"""

import sys
import time
from pathlib import Path
import uuid

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.core.download_manager import DownloadManager

def test_duplicate_detection():
    """Test duplicate download detection"""
    print("🧪 Testing Duplicate Download Detection...")
    
    try:
        # Initialize download manager
        print("  📦 Initializing download manager...")
        download_manager = DownloadManager()
        download_manager.start()
        
        # Create first download
        download_info_1 = {
            'id': str(uuid.uuid4()),
            'url': 'https://httpbin.org/json',
            'filename': 'test_file.json',
            'save_path': str(Path(__file__).parent / 'downloads'),
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  🚀 Adding first download...")
        success1 = download_manager.add_download(download_info_1)
        print(f"     First download result: {success1}")
        
        # Try to add same URL again
        download_info_2 = {
            'id': str(uuid.uuid4()),  # Different ID
            'url': 'https://httpbin.org/json',  # Same URL
            'filename': 'test_file_2.json',  # Different filename
            'save_path': str(Path(__file__).parent / 'downloads'),
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  🔄 Trying to add same URL again...")
        success2 = download_manager.add_download(download_info_2)
        print(f"     Second download result: {success2}")
        
        # Try to add same filename in same directory
        download_info_3 = {
            'id': str(uuid.uuid4()),  # Different ID
            'url': 'https://httpbin.org/ip',  # Different URL
            'filename': 'test_file.json',  # Same filename
            'save_path': str(Path(__file__).parent / 'downloads'),  # Same path
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  📁 Trying to add same filename in same directory...")
        success3 = download_manager.add_download(download_info_3)
        print(f"     Third download result: {success3}")
        
        # Try to add same filename in different directory (should work)
        download_info_4 = {
            'id': str(uuid.uuid4()),  # Different ID
            'url': 'https://httpbin.org/headers',  # Different URL
            'filename': 'test_file.json',  # Same filename
            'save_path': str(Path(__file__).parent / 'downloads' / 'subfolder'),  # Different path
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  📂 Trying to add same filename in different directory...")
        success4 = download_manager.add_download(download_info_4)
        print(f"     Fourth download result: {success4}")
        
        # Check results
        print("\n  📊 Test Results:")
        print(f"     First download (new): {success1} ✅ (should be True)")
        print(f"     Second download (same URL): {success2} ❌ (should be False)")
        print(f"     Third download (same filename/path): {success3} ❌ (should be False)")
        print(f"     Fourth download (same filename/different path): {success4} ✅ (should be True)")
        
        # Verify queue state
        active_downloads = len(download_manager.downloads)
        print(f"     Active downloads in queue: {active_downloads}")
        
        # Stop download manager
        download_manager.stop()
        
        # Evaluate test results
        expected_results = [True, False, False, True]
        actual_results = [success1, success2, success3, success4]
        
        if actual_results == expected_results:
            print("\n  🎉 All tests PASSED!")
            print("     Duplicate detection is working correctly!")
            return True
        else:
            print("\n  ❌ Some tests FAILED!")
            print(f"     Expected: {expected_results}")
            print(f"     Actual:   {actual_results}")
            return False
        
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def test_youtube_duplicate():
    """Test YouTube video duplicate detection"""
    print("\n🧪 Testing YouTube Duplicate Detection...")
    
    try:
        download_manager = DownloadManager()
        download_manager.start()
        
        # Create YouTube download
        youtube_info_1 = {
            'id': str(uuid.uuid4()),
            'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'filename': 'Rick Astley - Never Gonna Give You Up.mp4',
            'save_path': str(Path(__file__).parent / 'downloads' / 'Videos'),
            'category': 'Videos',
            'video_type': 'youtube',
            'quality': '720p',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'extension'
        }
        
        print("  🎬 Adding YouTube video...")
        success1 = download_manager.add_download(youtube_info_1)
        
        # Try to add same YouTube video again
        youtube_info_2 = {
            'id': str(uuid.uuid4()),  # Different ID
            'url': 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',  # Same URL
            'filename': 'Rick Astley - Never Gonna Give You Up.mp4',  # Same filename
            'save_path': str(Path(__file__).parent / 'downloads' / 'Videos'),
            'category': 'Videos',
            'video_type': 'youtube',
            'quality': '1080p',  # Different quality
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'extension'
        }
        
        print("  🔄 Trying to add same YouTube video again...")
        success2 = download_manager.add_download(youtube_info_2)
        
        download_manager.stop()
        
        print(f"     First YouTube download: {success1} ✅ (should be True)")
        print(f"     Second YouTube download: {success2} ❌ (should be False)")
        
        if success1 and not success2:
            print("  🎉 YouTube duplicate detection PASSED!")
            return True
        else:
            print("  ❌ YouTube duplicate detection FAILED!")
            return False
            
    except Exception as e:
        print(f"  ❌ YouTube test failed: {e}")
        return False

if __name__ == "__main__":
    # Test general duplicate detection
    success1 = test_duplicate_detection()
    
    # Test YouTube duplicate detection
    success2 = test_youtube_duplicate()
    
    if success1 and success2:
        print("\n✅ ALL TESTS PASSED!")
        print("Duplicate detection is working perfectly!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        if not success1:
            print("- General duplicate detection failed")
        if not success2:
            print("- YouTube duplicate detection failed")
