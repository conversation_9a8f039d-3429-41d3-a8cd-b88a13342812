// Background script for Python Download Manager extension

const SERVER_URL = 'http://localhost:9876';

// Create context menu items
chrome.runtime.onInstalled.addListener(() => {
  // Context menu for links
  chrome.contextMenus.create({
    id: 'download-link',
    title: 'تحميل باستخدام Python DM',
    contexts: ['link']
  });
  
  // Context menu for videos
  chrome.contextMenus.create({
    id: 'download-video',
    title: 'تحميل الفيديو',
    contexts: ['video']
  });
  
  // Context menu for pages (YouTube/TikTok)
  chrome.contextMenus.create({
    id: 'download-page',
    title: 'تحميل من هذه الصفحة',
    contexts: ['page'],
    documentUrlPatterns: [
      'https://www.youtube.com/watch*',
      'https://youtu.be/*',
      'https://www.tiktok.com/*',
      'https://vm.tiktok.com/*'
    ]
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  switch (info.menuItemId) {
    case 'download-link':
      downloadUrl(info.linkUrl, tab);
      break;
    case 'download-video':
      downloadUrl(info.srcUrl, tab);
      break;
    case 'download-page':
      downloadUrl(tab.url, tab);
      break;
  }
});

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'download':
      // Handle both old format (request.url) and new format (request.videoInfo)
      const url = request.url || (request.videoInfo && request.videoInfo.url);
      const options = request.options || {};

      // If we have videoInfo, extract additional data
      if (request.videoInfo) {
        options.title = request.videoInfo.title;
        options.type = request.videoInfo.type;
      }

      downloadUrl(url, sender.tab, options)
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true; // Keep message channel open for async response
      
    case 'analyze':
      analyzeUrl(request.url)
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true;
      
    case 'ping':
      pingServer()
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true;
      
    case 'getSettings':
      getSettings()
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true;
  }
});

// Download URL function
async function downloadUrl(url, tab, options = {}) {
  try {
    // Check if server is running
    await pingServer();

    // Generate filename from title if available
    let filename = options.filename;
    if (!filename && options.title) {
      filename = sanitizeFilename(options.title);
      if (options.type === 'youtube') {
        filename += '.mp4';
      }
    }
    if (!filename) {
      filename = extractFilenameFromUrl(url);
    }

    const downloadData = {
      url: url,
      filename: filename,
      quality: options.quality || 'best',
      category: options.category || detectCategory(url),
      source_tab: {
        title: options.title || tab?.title,
        url: tab?.url
      }
    };
    
    const response = await fetch(`${SERVER_URL}/download`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(downloadData)
    });
    
    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }
    
    const result = await response.json();
    
    // Show notification
    try {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Python Download Manager',
        message: 'تم إضافة التحميل بنجاح!'
      });
    } catch (notificationError) {
      console.log('Notification permission not granted:', notificationError);
    }
    
    return result;
    
  } catch (error) {
    console.error('Download error:', error);
    
    // Show error notification
    try {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Python Download Manager - خطأ',
        message: `فشل في إضافة التحميل: ${error.message}`
      });
    } catch (notificationError) {
      console.log('Notification permission not granted:', notificationError);
    }
    
    throw error;
  }
}

// Analyze URL function
async function analyzeUrl(url) {
  try {
    const response = await fetch(`${SERVER_URL}/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: url })
    });
    
    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error) {
    console.error('Analyze error:', error);
    throw error;
  }
}

// Ping server function
async function pingServer() {
  try {
    const response = await fetch(`${SERVER_URL}/ping`, {
      method: 'GET',
      timeout: 5000
    });
    
    if (!response.ok) {
      throw new Error('Server not responding');
    }
    
    return await response.json();
    
  } catch (error) {
    throw new Error('Python Download Manager غير متصل. تأكد من تشغيل البرنامج.');
  }
}

// Get settings function
async function getSettings() {
  try {
    const response = await fetch(`${SERVER_URL}/settings`, {
      method: 'GET'
    });
    
    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error) {
    console.error('Settings error:', error);
    throw error;
  }
}

// Utility functions
function extractFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop();
    
    if (filename && filename.includes('.')) {
      return filename;
    }
    
    // For video platforms, generate a default name
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return 'youtube_video.mp4';
    } else if (url.includes('tiktok.com')) {
      return 'tiktok_video.mp4';
    }
    
    return 'download';
  } catch {
    return 'download';
  }
}

function detectCategory(url) {
  if (url.includes('youtube.com') || url.includes('youtu.be') || url.includes('tiktok.com')) {
    return 'Videos';
  }

  const extension = url.split('.').pop().toLowerCase();

  if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
    return 'Videos';
  } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(extension)) {
    return 'Audio';
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return 'Archives';
  } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
    return 'Documents';
  }

  return 'Others';
}

function sanitizeFilename(filename) {
  // Remove invalid characters for filenames
  return filename
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim() // Remove leading/trailing spaces
    .substring(0, 200); // Limit length
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup, but we can also add additional logic here
  console.log('Extension icon clicked on tab:', tab.url);
});
