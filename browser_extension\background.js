// Background script for Python Download Manager extension

const SERVER_URL = 'http://localhost:9876';

// Create context menu items
chrome.runtime.onInstalled.addListener(() => {
  // Context menu for links
  chrome.contextMenus.create({
    id: 'download-link',
    title: 'تحميل باستخدام Python DM',
    contexts: ['link']
  });
  
  // Context menu for videos
  chrome.contextMenus.create({
    id: 'download-video',
    title: 'تحميل الفيديو',
    contexts: ['video']
  });
  
  // Context menu for pages (YouTube/TikTok)
  chrome.contextMenus.create({
    id: 'download-page',
    title: 'تحميل من هذه الصفحة',
    contexts: ['page'],
    documentUrlPatterns: [
      'https://www.youtube.com/watch*',
      'https://youtu.be/*',
      'https://www.tiktok.com/*',
      'https://vm.tiktok.com/*'
    ]
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  switch (info.menuItemId) {
    case 'download-link':
      downloadUrl(info.linkUrl, tab);
      break;
    case 'download-video':
      downloadUrl(info.srcUrl, tab);
      break;
    case 'download-page':
      downloadUrl(tab.url, tab);
      break;
  }
});

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  switch (request.action) {
    case 'download':
      // Handle both old format (request.url) and new format (request.videoInfo)
      const url = request.url || (request.videoInfo && request.videoInfo.url);
      const options = request.options || {};

      // If we have videoInfo, extract additional data
      if (request.videoInfo) {
        options.title = request.videoInfo.title;
        options.type = request.videoInfo.type;
      }

      // If we have format, add it to options
      if (request.format) {
        options.format = request.format;
      }

      // Validate URL
      if (!url) {
        sendResponse({success: false, error: 'لم يتم العثور على رابط صالح'});
        return true;
      }

      downloadUrl(url, sender.tab, options)
        .then(response => {
          console.log('Download successful:', response);
          sendResponse({success: true, data: response});
        })
        .catch(error => {
          console.error('Download failed:', error);
          const errorMessage = error.message || error.toString() || 'خطأ غير معروف';
          sendResponse({success: false, error: errorMessage});
        });
      return true; // Keep message channel open for async response

    case 'getFormats':
      // Get available formats for video
      const formatUrl = request.url || (request.videoInfo && request.videoInfo.url);

      if (!formatUrl) {
        sendResponse({success: false, error: 'لم يتم العثور على رابط صالح'});
        return true;
      }

      getVideoFormats(formatUrl, sender.tab)
        .then(response => {
          console.log('Formats retrieved:', response);
          sendResponse({success: true, formats: response});
        })
        .catch(error => {
          console.error('Get formats failed:', error);
          const errorMessage = error.message || error.toString() || 'خطأ غير معروف';
          sendResponse({success: false, error: errorMessage});
        });
      return true;

    case 'analyze':
      analyzeUrl(request.url)
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true;
      
    case 'ping':
      pingServer()
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true;
      
    case 'getSettings':
      getSettings()
        .then(response => sendResponse({success: true, data: response}))
        .catch(error => sendResponse({success: false, error: error.message}));
      return true;
  }
});

// Download URL function
async function downloadUrl(url, tab, options = {}) {
  console.log('downloadUrl called with:', { url, options });

  try {
    // Validate inputs
    if (!url || typeof url !== 'string') {
      throw new Error('Invalid URL provided');
    }

    if (!tab || !tab.url) {
      console.warn('No tab information provided');
    }

    // Check if server is running, try to start if not
    console.log('Checking server status...');
    await ensureServerRunning();

    // Generate filename from title if available
    let filename = options.filename;
    if (!filename && options.title) {
      filename = sanitizeFilename(options.title);
      if (options.type === 'youtube') {
        filename += '.mp4';
      }
    }
    if (!filename) {
      filename = extractFilenameFromUrl(url);
    }

    const downloadData = {
      url: url,
      title: options.title || tab?.title, // Add title directly
      filename: filename,
      quality: options.quality || 'best',
      category: options.category || detectCategory(url),
      source_tab: {
        title: options.title || tab?.title,
        url: tab?.url
      }
    };

    // Add format information if available
    if (options.format) {
      downloadData.format_info = options.format; // Use format_info to match server expectation
      downloadData.quality = options.format.quality || options.format.format_id || 'best';
    }
    
    const response = await fetch(`${SERVER_URL}/download`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(downloadData)
    });
    
    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }
    
    const result = await response.json();
    
    // Show notification
    try {
      if (chrome.notifications && chrome.notifications.create) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Python Download Manager',
          message: 'تم إضافة التحميل بنجاح!'
        });
      } else {
        console.log('Notifications API not available');
      }
    } catch (notificationError) {
      console.error('Failed to show success notification:', notificationError);
    }
    
    return result;
    
  } catch (error) {
    console.error('Download error:', error);
    console.error('Error type:', typeof error);
    console.error('Error constructor:', error.constructor.name);

    // Determine error type and message
    let errorMessage = 'خطأ غير معروف';

    try {
      // Handle different error types
      if (error && typeof error === 'object') {
        const errorStr = error.message || error.toString() || String(error);

        if (errorStr.includes('Failed to fetch') || errorStr.includes('NetworkError') || errorStr.includes('fetch')) {
          errorMessage = 'البرنامج غير متصل. يرجى تشغيل البرنامج الرئيسي';
        } else if (errorStr.includes('Server error') || errorStr.includes('500') || errorStr.includes('404')) {
          errorMessage = 'خطأ في الخادم. يرجى المحاولة مرة أخرى';
        } else if (errorStr.includes('timeout') || errorStr.includes('Timeout')) {
          errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى';
        } else if (error.message) {
          errorMessage = error.message;
        } else {
          errorMessage = errorStr;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
    } catch (processingError) {
      console.error('Error processing error message:', processingError);
      errorMessage = 'خطأ في معالجة الخطأ';
    }

    // Show error notification
    try {
      if (chrome.notifications && chrome.notifications.create) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Python Download Manager - خطأ',
          message: `فشل في إضافة التحميل: ${errorMessage}`
        });
      } else {
        console.log('Notifications API not available');
      }
    } catch (notificationError) {
      console.error('Failed to show notification:', notificationError);
    }

    // Create a new error with the processed message
    const processedError = new Error(errorMessage);
    processedError.originalError = error;
    throw processedError;
  }
}

// Analyze URL function
async function analyzeUrl(url) {
  try {
    const response = await fetch(`${SERVER_URL}/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url: url })
    });
    
    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error) {
    console.error('Analyze error:', error);
    throw error;
  }
}

// Ensure server is running, try to start if not
async function ensureServerRunning() {
  try {
    // First try to ping the server
    await pingServer();
    return true;
  } catch (error) {
    console.log('Server not running, attempting to start...');

    // Try to start the program
    const started = await tryStartProgram();
    if (started) {
      // Wait a bit for the server to start
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Try ping again
      try {
        await pingServer();
        return true;
      } catch (pingError) {
        throw new Error('فشل في بدء تشغيل البرنامج. يرجى تشغيله يدوياً.');
      }
    } else {
      throw new Error('Python Download Manager غير متصل. يرجى تشغيل البرنامج يدوياً.');
    }
  }
}

// Try to start the program
async function tryStartProgram() {
  try {
    // Create a notification to inform user
    try {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: 'Python Download Manager',
        message: 'البرنامج غير مُشغل. جاري فتح تعليمات التشغيل...'
      });
    } catch (notificationError) {
      console.log('Notification error:', notificationError);
    }

    // Method 1: Try to run the batch file (if user has set up file associations)
    try {
      const batchPath = 'file:///C:/Users/<USER>/Desktop/augment/python_download/start_download_manager.bat';
      // This will only work if user has configured file associations
      window.open(batchPath, '_blank');
    } catch (error) {
      console.log('Could not run batch file:', error);
    }

    // Method 2: Create a new tab with detailed instructions
    chrome.tabs.create({
      url: chrome.runtime.getURL('start_program.html'),
      active: true
    });

    // Method 3: Try to open Windows Terminal with the command (if available)
    try {
      const terminalCommand = `wt.exe -d "C:\\Users\\<USER>\\Desktop\\augment\\python_download" python main.py`;
      // This requires Windows Terminal to be installed and configured
      navigator.clipboard.writeText('cd "C:\\Users\\<USER>\\Desktop\\augment\\python_download" && python main.py');
    } catch (error) {
      console.log('Could not prepare terminal command:', error);
    }

    return true;

  } catch (error) {
    console.error('Error trying to start program:', error);
    return false;
  }
}

// Ping server function
async function pingServer() {
  try {
    const response = await fetch(`${SERVER_URL}/ping`, {
      method: 'GET',
      timeout: 5000
    });

    if (!response.ok) {
      throw new Error('Server not responding');
    }

    return await response.json();

  } catch (error) {
    throw new Error('Python Download Manager غير متصل.');
  }
}

// Get settings function
async function getSettings() {
  try {
    const response = await fetch(`${SERVER_URL}/settings`, {
      method: 'GET'
    });
    
    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error) {
    console.error('Settings error:', error);
    throw error;
  }
}

// Utility functions
function extractFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop();
    
    if (filename && filename.includes('.')) {
      return filename;
    }
    
    // For video platforms, generate a default name
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
      return 'youtube_video.mp4';
    } else if (url.includes('tiktok.com')) {
      return 'tiktok_video.mp4';
    }
    
    return 'download';
  } catch {
    return 'download';
  }
}

function detectCategory(url) {
  if (url.includes('youtube.com') || url.includes('youtu.be') || url.includes('tiktok.com')) {
    return 'Videos';
  }

  const extension = url.split('.').pop().toLowerCase();

  if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
    return 'Videos';
  } else if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(extension)) {
    return 'Audio';
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return 'Archives';
  } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(extension)) {
    return 'Documents';
  }

  return 'Others';
}

function sanitizeFilename(filename) {
  // Remove invalid characters for filenames
  return filename
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim() // Remove leading/trailing spaces
    .substring(0, 200); // Limit length
}

// Get video formats
async function getVideoFormats(url, tab) {
  try {
    console.log('Getting formats for URL:', url);

    // Check if server is running
    await ensureServerRunning();

    // Send request to get formats
    const response = await fetch('http://localhost:9876/formats', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: url,
        source: 'extension'
      })
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const result = await response.json();

    if (result.success && result.formats) {
      return result.formats;
    } else {
      throw new Error(result.error || 'Failed to get formats');
    }

  } catch (error) {
    console.error('Get formats error:', error);

    // Determine error type and message
    let errorMessage = 'خطأ غير معروف';

    try {
      if (error && typeof error === 'object') {
        const errorStr = error.message || error.toString() || String(error);

        if (errorStr.includes('Failed to fetch') || errorStr.includes('NetworkError') || errorStr.includes('fetch')) {
          errorMessage = 'البرنامج غير متصل. يرجى تشغيل البرنامج الرئيسي';
        } else if (errorStr.includes('Server error') || errorStr.includes('500') || errorStr.includes('404')) {
          errorMessage = 'خطأ في الخادم. يرجى المحاولة مرة أخرى';
        } else if (errorStr.includes('timeout') || errorStr.includes('Timeout')) {
          errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى';
        } else if (error.message) {
          errorMessage = error.message;
        } else {
          errorMessage = errorStr;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
    } catch (processingError) {
      console.error('Error processing error message:', processingError);
      errorMessage = 'خطأ في معالجة الخطأ';
    }

    // Create a new error with the processed message
    const processedError = new Error(errorMessage);
    processedError.originalError = error;
    throw processedError;
  }
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup, but we can also add additional logic here
  console.log('Extension icon clicked on tab:', tab.url);
});
