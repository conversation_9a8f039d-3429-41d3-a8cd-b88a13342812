#!/usr/bin/env python3
"""
Simple Download Manager - Working Version
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import customtkinter as ctk
import threading
import os
import sys
from pathlib import Path
import uuid
import requests
import yt_dlp

# Set appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class SimpleDownloadManager:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("مدير التحميل البسيط")
        self.root.geometry("800x600")
        
        self.downloads = {}
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title = ctk.CTkLabel(
            main_frame, 
            text="مدير التحميل البسيط", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # URL input section
        url_frame = ctk.CTkFrame(main_frame)
        url_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(url_frame, text="رابط التحميل:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=20, pady=(20, 5))
        
        self.url_entry = ctk.CTkEntry(
            url_frame,
            placeholder_text="الصق رابط YouTube أو أي ملف هنا...",
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.url_entry.pack(fill="x", padx=20, pady=(0, 10))
        
        # Filename input
        ctk.CTkLabel(url_frame, text="اسم الملف:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=20, pady=(10, 5))
        
        self.filename_entry = ctk.CTkEntry(
            url_frame,
            placeholder_text="اسم الملف (اختياري)",
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.filename_entry.pack(fill="x", padx=20, pady=(0, 10))
        
        # Download path
        path_frame = ctk.CTkFrame(url_frame)
        path_frame.pack(fill="x", padx=20, pady=(10, 20))
        
        self.path_var = tk.StringVar(value=str(Path.home() / "Downloads"))
        
        self.path_entry = ctk.CTkEntry(
            path_frame,
            textvariable=self.path_var,
            height=40
        )
        self.path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        browse_btn = ctk.CTkButton(
            path_frame,
            text="تصفح",
            command=self.browse_folder,
            width=80,
            height=40
        )
        browse_btn.pack(side="right")
        
        # Download button - BIG and VISIBLE
        self.download_btn = ctk.CTkButton(
            main_frame,
            text="🔽 بدء التحميل",
            command=self.start_download,
            width=300,
            height=60,
            font=ctk.CTkFont(size=20, weight="bold"),
            fg_color=("#FF6B35", "#E55A2B"),
            hover_color=("#E55A2B", "#FF6B35")
        )
        self.download_btn.pack(pady=30)
        
        # Progress section
        progress_frame = ctk.CTkFrame(main_frame)
        progress_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(progress_frame, text="حالة التحميل:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=20, pady=(20, 5))
        
        self.progress_bar = ctk.CTkProgressBar(progress_frame)
        self.progress_bar.pack(fill="x", padx=20, pady=10)
        self.progress_bar.set(0)
        
        self.status_label = ctk.CTkLabel(
            progress_frame, 
            text="جاهز للتحميل", 
            font=ctk.CTkFont(size=14)
        )
        self.status_label.pack(padx=20, pady=(0, 20))
        
    def browse_folder(self):
        """Browse for download folder"""
        folder = filedialog.askdirectory(initialdir=self.path_var.get())
        if folder:
            self.path_var.set(folder)
    
    def is_youtube_url(self, url):
        """Check if URL is YouTube"""
        return 'youtube.com' in url or 'youtu.be' in url
    
    def download_youtube(self, url, output_path, filename=None):
        """Download YouTube video"""
        try:
            def progress_hook(d):
                if d['status'] == 'downloading':
                    if 'total_bytes' in d:
                        progress = d['downloaded_bytes'] / d['total_bytes']
                        self.root.after(0, lambda: self.update_progress(progress, f"تحميل: {progress*100:.1f}%"))
                elif d['status'] == 'finished':
                    self.root.after(0, lambda: self.update_progress(1.0, "تم التحميل بنجاح!"))
            
            ydl_opts = {
                'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
                'format': 'best[height<=720]',  # 720p max for faster download
                'progress_hooks': [progress_hook],
            }
            
            if filename:
                ydl_opts['outtmpl'] = os.path.join(output_path, f'{filename}.%(ext)s')
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
                
            return True
            
        except Exception as e:
            self.root.after(0, lambda: self.update_progress(0, f"خطأ: {str(e)}"))
            return False
    
    def download_file(self, url, output_path, filename=None):
        """Download regular file"""
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            if not filename:
                filename = url.split('/')[-1] or 'downloaded_file'
            
            filepath = os.path.join(output_path, filename)
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = downloaded / total_size
                            self.root.after(0, lambda p=progress: self.update_progress(p, f"تحميل: {p*100:.1f}%"))
            
            self.root.after(0, lambda: self.update_progress(1.0, "تم التحميل بنجاح!"))
            return True
            
        except Exception as e:
            self.root.after(0, lambda: self.update_progress(0, f"خطأ: {str(e)}"))
            return False
    
    def update_progress(self, progress, status):
        """Update progress bar and status"""
        self.progress_bar.set(progress)
        self.status_label.configure(text=status)
        
        if progress >= 1.0:
            self.download_btn.configure(state="normal", text="🔽 بدء التحميل")
        
    def start_download(self):
        """Start download process"""
        url = self.url_entry.get().strip()
        filename = self.filename_entry.get().strip()
        output_path = self.path_var.get()
        
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط التحميل")
            return
        
        if not os.path.exists(output_path):
            messagebox.showerror("خطأ", "مجلد التحميل غير موجود")
            return
        
        # Disable button during download
        self.download_btn.configure(state="disabled", text="جاري التحميل...")
        self.update_progress(0, "بدء التحميل...")
        
        # Start download in separate thread
        def download_thread():
            try:
                if self.is_youtube_url(url):
                    success = self.download_youtube(url, output_path, filename)
                else:
                    success = self.download_file(url, output_path, filename)
                
                if success:
                    self.root.after(0, lambda: messagebox.showinfo("نجح", "تم التحميل بنجاح!"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("خطأ", "فشل في التحميل"))
                    
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ غير متوقع: {str(e)}"))
            finally:
                self.root.after(0, lambda: self.download_btn.configure(state="normal", text="🔽 بدء التحميل"))
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleDownloadManager()
    app.run()
