// Content script for Python Download Manager extension
// Runs on YouTube and TikTok pages

(function() {
    'use strict';
    
    let downloadButtons = [];
    
    // Initialize when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    function init() {
        console.log('Python Download Manager: Content script loaded');
        
        // Add download buttons based on the current site
        if (isYouTubePage()) {
            addYouTubeDownloadButton();
            // Watch for navigation changes on YouTube (SPA)
            watchForYouTubeNavigation();
        } else if (isTikTokPage()) {
            addTikTokDownloadButton();
            // Watch for navigation changes on TikTok (SPA)
            watchForTikTokNavigation();
        }
    }
    
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com');
    }
    
    function isTikTokPage() {
        return window.location.hostname.includes('tiktok.com');
    }
    
    function addYouTubeDownloadButton() {
        // Wait for YouTube's UI to load
        const checkForElements = setInterval(() => {
            const actionsContainer = document.querySelector('#actions-inner, #menu-container, .ytd-menu-renderer');
            
            if (actionsContainer && !document.querySelector('.pdm-download-btn')) {
                clearInterval(checkForElements);
                createYouTubeDownloadButton(actionsContainer);
            }
        }, 1000);
        
        // Clear interval after 30 seconds to avoid infinite checking
        setTimeout(() => clearInterval(checkForElements), 30000);
    }
    
    function createYouTubeDownloadButton(container) {
        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'pdm-download-btn pdm-youtube-btn';
        downloadBtn.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
            <span>تحميل</span>
        `;
        downloadBtn.title = 'تحميل باستخدام Python Download Manager';
        
        downloadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            showYouTubeDownloadDialog();
        });
        
        // Style the button to match YouTube's design
        downloadBtn.style.cssText = `
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #f1f1f1;
            border: none;
            border-radius: 18px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #030303;
            margin: 0 8px;
            transition: background-color 0.1s;
        `;
        
        downloadBtn.addEventListener('mouseenter', () => {
            downloadBtn.style.backgroundColor = '#e5e5e5';
        });
        
        downloadBtn.addEventListener('mouseleave', () => {
            downloadBtn.style.backgroundColor = '#f1f1f1';
        });
        
        container.appendChild(downloadBtn);
        downloadButtons.push(downloadBtn);
    }
    
    function addTikTokDownloadButton() {
        const checkForElements = setInterval(() => {
            const actionsContainer = document.querySelector('[data-e2e="video-actions"], .video-actions, .action-bar');
            
            if (actionsContainer && !document.querySelector('.pdm-download-btn')) {
                clearInterval(checkForElements);
                createTikTokDownloadButton(actionsContainer);
            }
        }, 1000);
        
        setTimeout(() => clearInterval(checkForElements), 30000);
    }
    
    function createTikTokDownloadButton(container) {
        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'pdm-download-btn pdm-tiktok-btn';
        downloadBtn.innerHTML = `
            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
        `;
        downloadBtn.title = 'تحميل باستخدام Python Download Manager';
        
        downloadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            showTikTokDownloadDialog();
        });
        
        // Style the button to match TikTok's design
        downloadBtn.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.12);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            color: white;
            margin: 8px 0;
            transition: background-color 0.2s;
        `;
        
        downloadBtn.addEventListener('mouseenter', () => {
            downloadBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        });
        
        downloadBtn.addEventListener('mouseleave', () => {
            downloadBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.12)';
        });
        
        container.appendChild(downloadBtn);
        downloadButtons.push(downloadBtn);
    }
    
    function showYouTubeDownloadDialog() {
        const currentUrl = window.location.href;
        
        // First, analyze the video
        chrome.runtime.sendMessage({
            action: 'analyze',
            url: currentUrl
        }, (response) => {
            if (response.success) {
                showDownloadDialog(response.data, 'youtube');
            } else {
                // Fallback to simple download
                chrome.runtime.sendMessage({
                    action: 'download',
                    url: currentUrl,
                    options: { quality: 'best' }
                });
            }
        });
    }
    
    function showTikTokDownloadDialog() {
        const currentUrl = window.location.href;
        
        chrome.runtime.sendMessage({
            action: 'analyze',
            url: currentUrl
        }, (response) => {
            if (response.success) {
                showDownloadDialog(response.data, 'tiktok');
            } else {
                // Fallback to simple download
                chrome.runtime.sendMessage({
                    action: 'download',
                    url: currentUrl,
                    options: { quality: 'best' }
                });
            }
        });
    }
    
    function showDownloadDialog(videoInfo, platform) {
        // Create modal dialog
        const modal = document.createElement('div');
        modal.className = 'pdm-modal';
        modal.innerHTML = `
            <div class="pdm-modal-content">
                <div class="pdm-modal-header">
                    <h3>تحميل ${platform === 'youtube' ? 'فيديو YouTube' : 'فيديو TikTok'}</h3>
                    <button class="pdm-close-btn">&times;</button>
                </div>
                <div class="pdm-modal-body">
                    <div class="pdm-video-info">
                        <h4>${videoInfo.title || 'فيديو'}</h4>
                        ${videoInfo.uploader ? `<p>بواسطة: ${videoInfo.uploader}</p>` : ''}
                        ${videoInfo.duration ? `<p>المدة: ${formatDuration(videoInfo.duration)}</p>` : ''}
                    </div>
                    <div class="pdm-quality-selection">
                        <label>الجودة:</label>
                        <select class="pdm-quality-select">
                            ${(videoInfo.available_qualities || ['best', 'worst']).map(quality => 
                                `<option value="${quality}">${quality}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="pdm-actions">
                        <button class="pdm-download-video-btn">تحميل الفيديو</button>
                        <button class="pdm-download-audio-btn">تحميل الصوت فقط</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        
        const modalContent = modal.querySelector('.pdm-modal-content');
        modalContent.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 400px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        `;
        
        // Event listeners
        modal.querySelector('.pdm-close-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
        
        modal.querySelector('.pdm-download-video-btn').addEventListener('click', () => {
            const quality = modal.querySelector('.pdm-quality-select').value;
            chrome.runtime.sendMessage({
                action: 'download',
                url: window.location.href,
                options: { quality: quality }
            });
            document.body.removeChild(modal);
        });
        
        modal.querySelector('.pdm-download-audio-btn').addEventListener('click', () => {
            chrome.runtime.sendMessage({
                action: 'download',
                url: window.location.href,
                options: { quality: 'audio', audioOnly: true }
            });
            document.body.removeChild(modal);
        });
        
        document.body.appendChild(modal);
    }
    
    function formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    function watchForYouTubeNavigation() {
        let currentUrl = window.location.href;
        
        const observer = new MutationObserver(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                
                // Remove old buttons
                downloadButtons.forEach(btn => {
                    if (btn.parentNode) {
                        btn.parentNode.removeChild(btn);
                    }
                });
                downloadButtons = [];
                
                // Add new button after a delay
                setTimeout(() => {
                    if (window.location.href.includes('/watch')) {
                        addYouTubeDownloadButton();
                    }
                }, 2000);
            }
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }
    
    function watchForTikTokNavigation() {
        let currentUrl = window.location.href;
        
        const observer = new MutationObserver(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                
                // Remove old buttons
                downloadButtons.forEach(btn => {
                    if (btn.parentNode) {
                        btn.parentNode.removeChild(btn);
                    }
                });
                downloadButtons = [];
                
                // Add new button after a delay
                setTimeout(() => {
                    addTikTokDownloadButton();
                }, 2000);
            }
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    }
    
})();
