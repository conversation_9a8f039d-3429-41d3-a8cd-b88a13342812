// Content script for Python Download Manager extension
// Adds download icon overlay to YouTube videos

(function() {
    'use strict';
    
    let downloadIcon = null;
    
    // Initialize when page loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    function init() {
        console.log('Python Download Manager: Content script loaded');
        
        // Add download icon for YouTube videos
        if (isYouTubePage()) {
            addYouTubeDownloadIcon();
            watchForYouTubeNavigation();
        }
    }
    
    function isYouTubePage() {
        return window.location.hostname.includes('youtube.com') && 
               window.location.pathname.includes('/watch');
    }
    
    // Create download icon overlay
    function createDownloadIcon() {
        const icon = document.createElement('div');
        icon.id = 'pdm-download-icon';
        icon.className = 'pdm-download-icon';
        icon.innerHTML = `
            <svg width="32" height="32" viewBox="0 0 24 24" fill="white">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
        `;
        icon.title = 'تحميل الفيديو بواسطة Python Download Manager';
        
        // Styling
        icon.style.cssText = `
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 1000;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            padding: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
            opacity: 0.9;
            border: 2px solid rgba(255, 255, 255, 0.2);
        `;
        
        // Hover effects
        icon.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(33, 150, 243, 0.9)';
            this.style.transform = 'scale(1.1)';
            this.style.opacity = '1';
            this.style.borderColor = 'rgba(255, 255, 255, 0.4)';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(0, 0, 0, 0.8)';
            this.style.transform = 'scale(1)';
            this.style.opacity = '0.9';
            this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        });
        
        // Click handler
        icon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            handleDownloadClick();
        });
        
        return icon;
    }
    
    function addYouTubeDownloadIcon() {
        // Remove existing icon
        const existingIcon = document.getElementById('pdm-download-icon');
        if (existingIcon) {
            existingIcon.remove();
        }
        
        // Wait for video player to load
        const checkForVideo = () => {
            const videoContainer = document.querySelector('#movie_player') || 
                                  document.querySelector('.html5-video-player') ||
                                  document.querySelector('#player-container') ||
                                  document.querySelector('#ytd-player');
            
            if (videoContainer) {
                const downloadIcon = createDownloadIcon();
                
                // Make sure container is positioned relatively
                if (getComputedStyle(videoContainer).position === 'static') {
                    videoContainer.style.position = 'relative';
                }
                
                videoContainer.appendChild(downloadIcon);
                console.log('PDM: Download icon added to YouTube video');
            } else {
                setTimeout(checkForVideo, 1000);
            }
        };
        
        checkForVideo();
    }
    
    // Handle download click
    function handleDownloadClick() {
        const videoInfo = getVideoInfo();
        if (!videoInfo) {
            showNotification('لا يمكن العثور على معلومات الفيديو', 'error');
            return;
        }

        // Visual feedback - show loading state
        const icon = document.getElementById('pdm-download-icon');
        if (icon) {
            icon.innerHTML = `
                <svg width="32" height="32" viewBox="0 0 24 24" fill="white">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                        <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                        <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                    </circle>
                </svg>
            `;
            icon.style.background = 'rgba(255, 152, 0, 0.9)';
            icon.style.transform = 'scale(1.1)';
            icon.title = 'جاري تحميل الصيغ المتاحة...';
        }

        // Get available formats first with timeout
        const formatTimeout = setTimeout(() => {
            console.warn('Format request timeout, falling back to direct download');
            resetDownloadIcon();
            showNotification('⚠️ انتهت مهلة تحميل الصيغ، سيتم التحميل بالجودة الافتراضية', 'warning');

            // Fallback to direct download
            chrome.runtime.sendMessage({
                action: 'download',
                videoInfo: videoInfo
            });
        }, 10000); // 10 second timeout

        try {
            chrome.runtime.sendMessage({
                action: 'getFormats',
                videoInfo: videoInfo
            }, (response) => {
                clearTimeout(formatTimeout);
                if (chrome.runtime.lastError) {
                    console.error('Chrome runtime error:', chrome.runtime.lastError);
                    const errorMessage = chrome.runtime.lastError.message || 'خطأ غير معروف';

                    if (errorMessage.includes('Extension context invalidated')) {
                        showNotification('❌ تم تحديث الإضافة. يرجى إعادة تحميل الصفحة', 'error');
                    } else if (errorMessage.includes('Could not establish connection')) {
                        showNotification('❌ خطأ في الاتصال مع الإضافة', 'error');
                    } else {
                        showNotification(`❌ خطأ في الإضافة: ${errorMessage}`, 'error');
                    }
                    resetDownloadIcon();
                    return;
                }

                if (!response) {
                    console.error('No response received from background script');
                    showNotification('❌ لم يتم تلقي رد من الإضافة', 'error');
                    resetDownloadIcon();
                    return;
                }

                if (response.success && response.formats) {
                    // Log cache status
                    if (response.cached) {
                        console.log(`✅ تم تحميل ${response.formats.length} صيغة من التخزين المؤقت`);
                    } else {
                        console.log(`✅ تم تحميل ${response.formats.length} صيغة جديدة`);
                    }

                    // Show format selection dialog
                    showFormatDialog(response.formats, videoInfo);
                } else {
                    const errorMsg = response.error || 'خطأ غير معروف';
                    console.error('Get formats error:', errorMsg);

                    if (errorMsg.includes('غير متصل') || errorMsg.includes('not responding') || errorMsg.includes('Failed to fetch')) {
                        showNotification('❌ البرنامج غير مُشغل. سيتم فتح تعليمات التشغيل...', 'error');
                    } else {
                        showNotification(`❌ خطأ في الحصول على صيغ الفيديو: ${errorMsg}`, 'error');
                    }
                    resetDownloadIcon();
                }
            });
        } catch (error) {
            console.error('Error sending message:', error);
            showNotification('❌ خطأ في إرسال الطلب', 'error');
            resetDownloadIcon();
        }
        
    }

    function resetDownloadIcon() {
        const icon = document.getElementById('pdm-download-icon');
        if (icon) {
            icon.innerHTML = `
                <svg width="32" height="32" viewBox="0 0 24 24" fill="white">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                </svg>
            `;
            icon.style.background = 'rgba(0, 0, 0, 0.8)';
            icon.style.transform = 'scale(1)';
        }
    }
    
    // Get video information
    function getVideoInfo() {
        const url = window.location.href;
        const title = getYouTubeTitle();
        
        return {
            type: 'youtube',
            url: url,
            title: title,
            timestamp: Date.now()
        };
    }
    
    // Get YouTube video title
    function getYouTubeTitle() {
        const titleSelectors = [
            'h1.ytd-video-primary-info-renderer',
            '.ytd-video-primary-info-renderer h1',
            '#container h1',
            'h1[class*="title"]',
            'meta[property="og:title"]'
        ];
        
        for (const selector of titleSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const title = element.textContent || element.getAttribute('content');
                if (title && title.trim()) {
                    return title.trim();
                }
            }
        }
        
        return document.title || 'YouTube Video';
    }
    
    // Show notification
    function showNotification(message, type = 'success') {
        // Remove existing notification
        const existingNotification = document.getElementById('pdm-notification');
        if (existingNotification) {
            existingNotification.remove();
        }
        
        // Create notification
        const notification = document.createElement('div');
        notification.id = 'pdm-notification';
        notification.textContent = message;
        
        const bgColor = type === 'success' ? 'rgba(76, 175, 80, 0.95)' : 'rgba(244, 67, 54, 0.95)';
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            max-width: 350px;
            text-align: center;
            animation: slideInRight 0.3s ease-out;
        `;
        
        // Add animation styles
        if (!document.getElementById('pdm-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'pdm-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(notification);
        
        // Remove after 4 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }
    
    // Show format selection dropdown
    function showFormatDialog(formats, videoInfo) {
        // Remove existing dropdown if any
        const existingDropdown = document.getElementById('pdm-format-dropdown');
        if (existingDropdown) {
            existingDropdown.remove();
        }

        // Get download icon position
        const downloadIcon = document.getElementById('pdm-download-icon');
        if (!downloadIcon) {
            console.error('Download icon not found');
            return;
        }

        const iconRect = downloadIcon.getBoundingClientRect();

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.id = 'pdm-format-dropdown';
        dropdown.style.cssText = `
            position: fixed;
            top: ${iconRect.bottom + 8}px;
            left: ${iconRect.left}px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            min-width: 300px;
            max-width: 400px;
            max-height: 400px;
            overflow-y: auto;
            font-family: Arial, sans-serif;
            border: 1px solid #ddd;
        `;

        // Header
        const header = document.createElement('div');
        header.style.cssText = `
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;

        const title = document.createElement('div');
        title.textContent = 'اختر صيغة التحميل';
        title.style.cssText = `
            font-weight: 600;
            color: #333;
            font-size: 14px;
        `;

        const closeBtn = document.createElement('button');
        closeBtn.textContent = '✕';
        closeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #666;
            padding: 2px 6px;
            border-radius: 3px;
            line-height: 1;
        `;
        closeBtn.onclick = () => {
            dropdown.remove();
            resetDownloadIcon();
        };

        header.appendChild(title);
        header.appendChild(closeBtn);

        // Format list
        const formatList = document.createElement('div');
        formatList.style.cssText = `
            max-height: 320px;
            overflow-y: auto;
        `;

        // Separate video and audio formats
        const videoFormats = formats
            .filter(format => format.height > 0) // Video formats with height
            .sort((a, b) => (b.height || 0) - (a.height || 0)); // Sort by quality (highest first)

        // Audio-only formats
        const audioFormats = formats
            .filter(format => format.acodec && format.acodec !== 'none' && (!format.height || format.height === 0))
            .sort((a, b) => (b.tbr || 0) - (a.tbr || 0)) // Sort by bitrate
            .slice(0, 3); // Limit to 3 best audio formats

        // Mixed formats (video + audio combined)
        const mixedFormats = formats
            .filter(format => format.acodec && format.acodec !== 'none' && format.height > 0)
            .sort((a, b) => (b.height || 0) - (a.height || 0))
            .slice(0, 5); // Limit to 5 best mixed formats

        // Combine all formats: mixed first (best quality), then video-only, then audio-only
        const allFormats = [...mixedFormats, ...videoFormats, ...audioFormats];

        // Add formats to dropdown
        allFormats.forEach((format, index) => {
            const formatItem = document.createElement('div');
            formatItem.style.cssText = `
                padding: 10px 16px;
                cursor: pointer;
                transition: background 0.2s;
                border-bottom: 1px solid #f0f0f0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            `;

            formatItem.onmouseover = () => {
                formatItem.style.background = '#f0f8ff';
            };

            formatItem.onmouseout = () => {
                formatItem.style.background = 'white';
            };

            const formatInfo = document.createElement('div');
            const isAudio = !format.height || format.height === 0;
            const qualityLabel = isAudio ? 'صوت فقط' : format.quality || `${format.height}p`;

            // Get quality icon
            const getQualityIcon = (format) => {
                const height = format.height || 0;
                const hasAudio = format.acodec && format.acodec !== 'none';
                const hasVideo = format.vcodec && format.vcodec !== 'none';

                // Audio-only
                if (!hasVideo && hasAudio) return '🎵';

                // Mixed format (video + audio)
                if (hasVideo && hasAudio) {
                    if (height >= 2160) return '🎬'; // 4K with audio
                    if (height >= 1080) return '📺'; // 1080p with audio
                    if (height >= 720) return '🎥'; // 720p with audio
                    return '📱'; // Lower quality with audio
                }

                // Video-only
                if (hasVideo && !hasAudio) {
                    if (height >= 2160) return '🎞️'; // 4K video only
                    if (height >= 1080) return '📹'; // 1080p video only
                    if (height >= 720) return '🎦'; // 720p video only
                    return '📽️'; // Lower quality video only
                }

                return '❓'; // Unknown format
            };

            // Enhanced format info display
            const hasAudio = format.acodec && format.acodec !== 'none';
            const hasVideo = format.vcodec && format.vcodec !== 'none';
            const formatType = hasVideo && hasAudio ? 'مختلط' : hasVideo ? 'فيديو فقط' : 'صوت فقط';

            formatInfo.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 2px;">
                    <span style="margin-right: 6px; font-size: 14px;">${getQualityIcon(format)}</span>
                    <span style="font-weight: 500; color: #333; font-size: 13px;">
                        ${qualityLabel} - ${format.ext || 'mp4'}
                    </span>
                    <span style="font-size: 10px; color: #888; margin-left: 6px; background: #f0f0f0; padding: 1px 4px; border-radius: 3px;">
                        ${formatType}
                    </span>
                </div>
                <div style="font-size: 11px; color: #666; margin-left: 20px;">
                    ${format.filesize ? formatFileSize(format.filesize) : 'حجم غير معروف'}
                    ${format.fps && hasVideo ? ` • ${format.fps}fps` : ''}
                    ${format.tbr ? ` • ${format.tbr}kbps` : ''}
                </div>
            `;

            const downloadIcon = document.createElement('div');
            downloadIcon.innerHTML = '⬇️';
            downloadIcon.style.cssText = `
                font-size: 16px;
                opacity: 0.7;
                transition: opacity 0.2s;
            `;

            formatItem.onmouseover = () => {
                formatItem.style.background = '#f0f8ff';
                downloadIcon.style.opacity = '1';
            };

            formatItem.onmouseout = () => {
                formatItem.style.background = 'white';
                downloadIcon.style.opacity = '0.7';
            };

            formatItem.onclick = () => {
                downloadWithFormat(videoInfo, format);
                dropdown.remove();
            };

            formatItem.appendChild(formatInfo);
            formatItem.appendChild(downloadIcon);
            formatList.appendChild(formatItem);
        });

        // Assemble dropdown
        dropdown.appendChild(header);
        dropdown.appendChild(formatList);
        document.body.appendChild(dropdown);

        // Close dropdown when clicking outside
        const closeDropdown = (e) => {
            if (!dropdown.contains(e.target) && e.target !== downloadIcon) {
                dropdown.remove();
                resetDownloadIcon();
                document.removeEventListener('click', closeDropdown);
            }
        };

        // Add click listener after a short delay to prevent immediate closing
        setTimeout(() => {
            document.addEventListener('click', closeDropdown);
        }, 100);

        // Adjust position if dropdown goes off screen
        setTimeout(() => {
            const dropdownRect = dropdown.getBoundingClientRect();
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            // Adjust horizontal position
            if (dropdownRect.right > windowWidth) {
                dropdown.style.left = `${windowWidth - dropdownRect.width - 10}px`;
            }

            // Adjust vertical position
            if (dropdownRect.bottom > windowHeight) {
                dropdown.style.top = `${iconRect.top - dropdownRect.height - 8}px`;
            }
        }, 10);
    }

    // Format file size
    function formatFileSize(bytes) {
        if (!bytes) return 'غير معروف';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    // Download with specific format
    function downloadWithFormat(videoInfo, format) {
        const icon = document.getElementById('pdm-download-icon');
        if (icon) {
            icon.innerHTML = `
                <svg width="32" height="32" viewBox="0 0 24 24" fill="white">
                    <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                </svg>
            `;
            icon.style.background = 'rgba(76, 175, 80, 0.9)';
        }

        // Send download request with specific format
        chrome.runtime.sendMessage({
            action: 'download',
            videoInfo: videoInfo,
            format: format
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('Chrome runtime error:', chrome.runtime.lastError);
                showNotification('❌ خطأ في الاتصال مع الإضافة', 'error');
                resetDownloadIcon();
                return;
            }

            if (!response) {
                showNotification('❌ لم يتم تلقي رد من الإضافة', 'error');
                resetDownloadIcon();
                return;
            }

            if (response.success) {
                showNotification(`✅ تم إضافة الفيديو بصيغة ${format.quality} لقائمة التحميل!`, 'success');
                setTimeout(resetDownloadIcon, 3000);
            } else {
                const errorMsg = response.error || 'خطأ غير معروف';
                showNotification(`❌ خطأ في إضافة الفيديو: ${errorMsg}`, 'error');
                resetDownloadIcon();
            }
        });
    }

    // Watch for YouTube navigation changes
    function watchForYouTubeNavigation() {
        let lastUrl = location.href;

        const observer = new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl && url.includes('youtube.com/watch')) {
                lastUrl = url;
                setTimeout(addYouTubeDownloadIcon, 2000);
            }
        });

        observer.observe(document, { subtree: true, childList: true });
    }
    
})();
