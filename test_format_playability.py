#!/usr/bin/env python3
"""
اختبار قابلية تشغيل الصيغ المختلفة
"""

import requests
import json

SERVER_URL = "http://localhost:9876"
TEST_URL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"

def test_format_analysis():
    """اختبار تحليل الصيغ وقابلية التشغيل"""
    print("🧪 اختبار تحليل الصيغ وقابلية التشغيل")
    print("=" * 60)
    
    try:
        # طلب الصيغ
        response = requests.post(
            f"{SERVER_URL}/formats",
            json={"url": TEST_URL},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                formats = data.get('formats', [])
                
                print(f"✅ تم الحصول على {len(formats)} صيغة")
                print("\n📊 تحليل الصيغ:")
                print("-" * 80)
                
                # تصنيف الصيغ
                complete_formats = []
                video_only_formats = []
                audio_only_formats = []
                unknown_formats = []
                
                for fmt in formats:
                    format_type = fmt.get('format_type', 'unknown')
                    playable = fmt.get('playable', False)
                    needs_merge = fmt.get('needs_merge', False)
                    
                    if format_type == 'complete':
                        complete_formats.append(fmt)
                    elif format_type == 'video_only':
                        video_only_formats.append(fmt)
                    elif format_type == 'audio_only':
                        audio_only_formats.append(fmt)
                    else:
                        unknown_formats.append(fmt)
                
                # عرض الصيغ الكاملة (قابلة للتشغيل)
                if complete_formats:
                    print(f"\n✅ صيغ كاملة (قابلة للتشغيل): {len(complete_formats)}")
                    for fmt in complete_formats[:5]:  # أول 5 صيغ
                        size = fmt.get('filesize')
                        size_str = f"{size/1024/1024:.1f}MB" if size else "غير معروف"
                        print(f"   🎬 {fmt['quality']} - {fmt['ext']} - {size_str} - ID:{fmt['format_id']}")
                
                # عرض الصيغ المرئية فقط (تحتاج دمج)
                if video_only_formats:
                    print(f"\n⚠️ صيغ فيديو فقط (تحتاج دمج): {len(video_only_formats)}")
                    for fmt in video_only_formats[:5]:  # أول 5 صيغ
                        size = fmt.get('filesize')
                        size_str = f"{size/1024/1024:.1f}MB" if size else "غير معروف"
                        needs_merge = "✓" if fmt.get('needs_merge') else "✗"
                        print(f"   📹 {fmt['quality']} - {fmt['ext']} - {size_str} - دمج:{needs_merge} - ID:{fmt['format_id']}")
                
                # عرض الصيغ الصوتية فقط
                if audio_only_formats:
                    print(f"\n🎵 صيغ صوت فقط: {len(audio_only_formats)}")
                    for fmt in audio_only_formats:
                        size = fmt.get('filesize')
                        size_str = f"{size/1024/1024:.1f}MB" if size else "غير معروف"
                        tbr = fmt.get('tbr', 0)
                        print(f"   🎶 {fmt['quality']} - {fmt['ext']} - {size_str} - {tbr}kbps - ID:{fmt['format_id']}")
                
                # عرض الصيغ غير المعروفة
                if unknown_formats:
                    print(f"\n❓ صيغ غير معروفة: {len(unknown_formats)}")
                    for fmt in unknown_formats:
                        print(f"   ❓ {fmt.get('quality', 'غير محدد')} - {fmt['ext']} - ID:{fmt['format_id']}")
                
                # إحصائيات
                print(f"\n📈 الإحصائيات:")
                print(f"   ✅ صيغ قابلة للتشغيل: {len(complete_formats) + len(audio_only_formats)}")
                print(f"   ⚠️ صيغ تحتاج دمج: {len(video_only_formats)}")
                print(f"   ❓ صيغ غير معروفة: {len(unknown_formats)}")
                print(f"   📊 إجمالي الصيغ: {len(formats)}")
                
                # توصيات
                print(f"\n💡 التوصيات:")
                if complete_formats:
                    best_complete = complete_formats[0]
                    print(f"   🏆 أفضل صيغة كاملة: {best_complete['quality']} - {best_complete['ext']} (ID: {best_complete['format_id']})")
                
                if video_only_formats:
                    best_video_only = video_only_formats[0]
                    print(f"   🎬 أفضل صيغة فيديو (تحتاج دمج): {best_video_only['quality']} - {best_video_only['ext']} (ID: {best_video_only['format_id']})")
                
                if audio_only_formats:
                    best_audio = audio_only_formats[0]
                    print(f"   🎵 أفضل صيغة صوت: {best_audio['quality']} - {best_audio['ext']} (ID: {best_audio['format_id']})")
                
                return True
                
            else:
                print(f"❌ فشل: {data.get('error', 'خطأ غير معروف')}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_download_different_formats():
    """اختبار تحميل صيغ مختلفة"""
    print("\n🎬 اختبار تحميل صيغ مختلفة")
    print("=" * 60)
    
    # صيغ للاختبار
    test_formats = [
        {"format_id": "18", "quality": "360p", "type": "complete", "description": "صيغة كاملة منخفضة الجودة"},
        {"format_id": "22", "quality": "720p", "type": "complete", "description": "صيغة كاملة متوسطة الجودة"},
        {"format_id": "137", "quality": "1080p", "type": "video_only", "description": "فيديو فقط عالي الجودة"},
        {"format_id": "140", "quality": "128kbps", "type": "audio_only", "description": "صوت فقط عالي الجودة"},
    ]
    
    for test_format in test_formats:
        print(f"\n🧪 اختبار: {test_format['description']}")
        print(f"   📋 الصيغة: {test_format['format_id']} ({test_format['quality']})")
        print(f"   🎯 النوع: {test_format['type']}")
        
        # محاكاة طلب التحميل (بدون تحميل فعلي)
        format_info = {
            "format_id": test_format['format_id'],
            "quality": test_format['quality'],
            "format_type": test_format['type'],
            "needs_merge": test_format['type'] == 'video_only'
        }
        
        try:
            # هنا يمكن إضافة طلب تحميل فعلي للاختبار
            # response = requests.post(f"{SERVER_URL}/download", json={
            #     "url": TEST_URL,
            #     "format_info": format_info
            # })
            
            print(f"   ✅ جاهز للتحميل")
            if test_format['type'] == 'video_only':
                print(f"   ⚠️ سيتم دمج الفيديو مع الصوت تلقائياً")
            elif test_format['type'] == 'complete':
                print(f"   ✅ صيغة كاملة - ستعمل مباشرة")
            elif test_format['type'] == 'audio_only':
                print(f"   🎵 صوت فقط - مناسب للموسيقى")
                
        except Exception as e:
            print(f"   ❌ خطأ في الاختبار: {str(e)}")

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    print("🔌 اختبار الاتصال بالخادم...")
    
    try:
        response = requests.get(f"{SERVER_URL}/ping", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok':
                print("✅ الخادم متصل ويعمل")
                return True
            else:
                print(f"⚠️ الخادم يرد لكن بحالة غير طبيعية: {data}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار قابلية تشغيل الصيغ المختلفة")
    print("=" * 70)
    
    # اختبار الاتصال أولاً
    if not test_server_connection():
        print("\n❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل البرنامج أولاً.")
        return
    
    # اختبار تحليل الصيغ
    success = test_format_analysis()
    
    if success:
        # اختبار تحميل صيغ مختلفة
        test_download_different_formats()
        
        print("\n🎉 الاختبار اكتمل بنجاح!")
        print("\n💡 نصائح للاستخدام:")
        print("- اختر الصيغ الكاملة (✅) للتشغيل المباشر")
        print("- الصيغ المرئية فقط (⚠️) ستُدمج تلقائياً مع الصوت")
        print("- الصيغ الصوتية (🎵) مناسبة لتحميل الموسيقى")
        print("- تجنب الصيغ غير المعروفة (❓) إذا كنت غير متأكد")
    else:
        print("\n❌ الاختبار فشل. تحقق من حالة الخادم والاتصال.")

if __name__ == "__main__":
    main()
