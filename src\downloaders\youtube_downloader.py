"""
YouTube Video Downloader using yt-dlp
"""

import yt_dlp
import os
import sys
from pathlib import Path
import json
import threading
import time

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger
from src.utils.file_utils import sanitize_filename, ensure_unique_filename

class YouTubeDownloader:
    def __init__(self, progress_callback=None):
        self.progress_callback = progress_callback
        self.logger = get_logger(__name__)
        self.is_cancelled = False
        
        # yt-dlp options
        self.ydl_opts = {
            'format': 'best',
            'outtmpl': '%(title)s.%(ext)s',
            'noplaylist': True,
            'extractaudio': False,
            'audioformat': 'mp3',
            'embed_subs': True,
            'writesubtitles': True,
            'writeautomaticsub': True,
            'ignoreerrors': True,
            # Enable automatic merging for video-only formats
            'merge_output_format': 'mp4',
            'postprocessors': [],  # Will be configured per download
        }
    
    def get_video_info(self, url):
        """Get video information without downloading"""
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    return None
                
                # Extract relevant information
                video_info = {
                    'id': info.get('id'),
                    'title': info.get('title'),
                    'description': info.get('description'),
                    'duration': info.get('duration'),
                    'uploader': info.get('uploader'),
                    'upload_date': info.get('upload_date'),
                    'view_count': info.get('view_count'),
                    'like_count': info.get('like_count'),
                    'thumbnail': info.get('thumbnail'),
                    'formats': []
                }
                
                # Extract available formats
                if 'formats' in info:
                    for fmt in info['formats']:
                        if fmt.get('vcodec') != 'none':  # Video formats
                            format_info = {
                                'format_id': fmt.get('format_id'),
                                'ext': fmt.get('ext'),
                                'resolution': fmt.get('resolution') or f"{fmt.get('width', 0)}x{fmt.get('height', 0)}",
                                'fps': fmt.get('fps'),
                                'vcodec': fmt.get('vcodec'),
                                'acodec': fmt.get('acodec'),
                                'filesize': fmt.get('filesize'),
                                'quality': self._get_quality_label(fmt)
                            }
                            video_info['formats'].append(format_info)
                
                return video_info
                
        except Exception as e:
            self.logger.error(f"Error getting video info: {e}")
            return None
    
    def _get_quality_label(self, format_info):
        """Get quality label for format"""
        height = format_info.get('height')
        if height:
            if height >= 2160:
                return '4K'
            elif height >= 1440:
                return '1440p'
            elif height >= 1080:
                return '1080p'
            elif height >= 720:
                return '720p'
            elif height >= 480:
                return '480p'
            elif height >= 360:
                return '360p'
            elif height >= 240:
                return '240p'
            else:
                return '144p'
        
        return format_info.get('resolution', 'Unknown')
    
    def download_video(self, url, output_path, quality='best', audio_only=False, format_info=None):
        """Download video from YouTube"""
        try:
            self.is_cancelled = False

            # Setup output template
            output_template = os.path.join(output_path, '%(title)s.%(ext)s')

            # Configure yt-dlp options
            ydl_opts = self.ydl_opts.copy()
            ydl_opts['outtmpl'] = output_template

            # Set quality/format
            if format_info and format_info.get('format_id'):
                # Use specific format ID
                format_id = format_info['format_id']

                # Check if format needs merging (video-only)
                if format_info.get('needs_merge') or format_info.get('format_type') == 'video_only':
                    # For video-only formats, try to merge with best audio
                    ydl_opts['format'] = f'{format_id}+bestaudio/best'
                    self.logger.info(f"Using video-only format with audio merge: {format_id} ({format_info.get('quality', 'Unknown')})")

                    # Ensure we have postprocessors for merging
                    ydl_opts['postprocessors'] = [{
                        'key': 'FFmpegVideoConvertor',
                        'preferedformat': 'mp4',
                    }]
                else:
                    # Use format as-is (complete format)
                    ydl_opts['format'] = format_id
                    self.logger.info(f"Using complete format: {format_id} ({format_info.get('quality', 'Unknown')})")

            elif audio_only:
                ydl_opts['format'] = 'bestaudio/best'
                ydl_opts['extractaudio'] = True
                ydl_opts['audioformat'] = 'mp3'
            else:
                if quality == 'best':
                    ydl_opts['format'] = 'best'
                elif quality == 'worst':
                    ydl_opts['format'] = 'worst'
                else:
                    # Try to match specific quality
                    ydl_opts['format'] = f'best[height<={quality[:-1]}]' if quality.endswith('p') else quality
            
            # Add progress hook
            ydl_opts['progress_hooks'] = [self._progress_hook]
            
            # Download
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            return True
            
        except Exception as e:
            if not self.is_cancelled:
                self.logger.error(f"Error downloading video: {e}")
            return False
    
    def _progress_hook(self, d):
        """Progress hook for yt-dlp"""
        if self.is_cancelled:
            raise yt_dlp.DownloadError("Download cancelled by user")
        
        if self.progress_callback:
            status = d.get('status')
            
            if status == 'downloading':
                progress_info = {
                    'status': 'downloading',
                    'filename': d.get('filename', ''),
                    'downloaded_bytes': d.get('downloaded_bytes', 0),
                    'total_bytes': d.get('total_bytes') or d.get('total_bytes_estimate', 0),
                    'speed': d.get('speed', 0),
                    'eta': d.get('eta', 0)
                }
                
                # Calculate progress percentage
                if progress_info['total_bytes'] > 0:
                    progress_info['progress'] = (progress_info['downloaded_bytes'] / progress_info['total_bytes']) * 100
                else:
                    progress_info['progress'] = 0
                
                self.progress_callback(progress_info)
                
            elif status == 'finished':
                progress_info = {
                    'status': 'completed',
                    'filename': d.get('filename', ''),
                    'total_bytes': d.get('total_bytes', 0),
                    'progress': 100
                }
                self.progress_callback(progress_info)
    
    def cancel_download(self):
        """Cancel ongoing download"""
        self.is_cancelled = True
        self.logger.info("YouTube download cancelled")
    
    def get_available_qualities(self, url):
        """Get available video qualities"""
        try:
            video_info = self.get_video_info(url)
            if not video_info or 'formats' not in video_info:
                return []
            
            qualities = set()
            for fmt in video_info['formats']:
                quality = fmt.get('quality')
                if quality and quality != 'Unknown':
                    qualities.add(quality)
            
            # Sort qualities
            quality_order = ['4K', '1440p', '1080p', '720p', '480p', '360p', '240p', '144p']
            available_qualities = []
            
            for quality in quality_order:
                if quality in qualities:
                    available_qualities.append(quality)
            
            # Add generic options
            available_qualities.insert(0, 'best')
            available_qualities.append('worst')
            
            return available_qualities
            
        except Exception as e:
            self.logger.error(f"Error getting available qualities: {e}")
            return ['best', 'worst']
    
    def download_playlist(self, url, output_path, quality='best', max_downloads=None):
        """Download YouTube playlist"""
        try:
            self.is_cancelled = False
            
            # Setup output template
            output_template = os.path.join(output_path, '%(playlist_index)s - %(title)s.%(ext)s')
            
            # Configure yt-dlp options
            ydl_opts = self.ydl_opts.copy()
            ydl_opts['outtmpl'] = output_template
            ydl_opts['noplaylist'] = False
            
            if max_downloads:
                ydl_opts['playlistend'] = max_downloads
            
            # Set quality
            if quality == 'best':
                ydl_opts['format'] = 'best'
            elif quality == 'worst':
                ydl_opts['format'] = 'worst'
            else:
                ydl_opts['format'] = f'best[height<={quality[:-1]}]' if quality.endswith('p') else quality
            
            # Add progress hook
            ydl_opts['progress_hooks'] = [self._progress_hook]
            
            # Download
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            return True
            
        except Exception as e:
            if not self.is_cancelled:
                self.logger.error(f"Error downloading playlist: {e}")
            return False
    
    def extract_audio(self, url, output_path, audio_format='mp3'):
        """Extract audio from YouTube video"""
        try:
            self.is_cancelled = False
            
            # Setup output template
            output_template = os.path.join(output_path, '%(title)s.%(ext)s')
            
            # Configure yt-dlp options for audio extraction
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': output_template,
                'extractaudio': True,
                'audioformat': audio_format,
                'audioquality': '192K',
                'progress_hooks': [self._progress_hook],
                'ignoreerrors': True,
            }
            
            # Download and extract audio
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            return True
            
        except Exception as e:
            if not self.is_cancelled:
                self.logger.error(f"Error extracting audio: {e}")
            return False
    
    def get_video_thumbnail(self, url, output_path):
        """Download video thumbnail"""
        try:
            video_info = self.get_video_info(url)
            if not video_info or not video_info.get('thumbnail'):
                return None
            
            import requests
            from urllib.parse import urlparse
            
            thumbnail_url = video_info['thumbnail']
            
            # Get file extension from URL
            parsed_url = urlparse(thumbnail_url)
            ext = os.path.splitext(parsed_url.path)[1] or '.jpg'
            
            # Create filename
            title = sanitize_filename(video_info.get('title', 'thumbnail'))
            filename = f"{title}_thumbnail{ext}"
            filepath = os.path.join(output_path, filename)
            
            # Download thumbnail
            response = requests.get(thumbnail_url, timeout=30)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error downloading thumbnail: {e}")
            return None
    
    def is_valid_youtube_url(self, url):
        """Check if URL is a valid YouTube URL"""
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                return info is not None
        except:
            return False
