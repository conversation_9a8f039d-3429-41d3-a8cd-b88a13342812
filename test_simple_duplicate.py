#!/usr/bin/env python3
"""
Simple test for duplicate detection
"""

import sys
from pathlib import Path
import uuid

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.core.download_manager import DownloadManager

def test_simple_duplicate():
    """Simple duplicate test"""
    print("🧪 Testing Simple Duplicate Detection...")
    
    try:
        download_manager = DownloadManager()
        download_manager.start()
        
        # Create first download
        download1 = {
            'id': str(uuid.uuid4()),
            'url': 'https://example.com/file1.zip',
            'filename': 'file1.zip',
            'save_path': '/downloads',
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  📥 Adding first download...")
        result1 = download_manager.add_download(download1)
        print(f"     Result: {result1}")
        
        # Try same URL
        download2 = {
            'id': str(uuid.uuid4()),
            'url': 'https://example.com/file1.zip',  # Same URL
            'filename': 'file2.zip',  # Different name
            'save_path': '/downloads',
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  🔄 Trying same URL...")
        result2 = download_manager.add_download(download2)
        print(f"     Result: {result2}")
        
        # Try same filename in same path
        download3 = {
            'id': str(uuid.uuid4()),
            'url': 'https://example.com/different.zip',  # Different URL
            'filename': 'file1.zip',  # Same filename
            'save_path': '/downloads',  # Same path
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  📁 Trying same filename in same path...")
        result3 = download_manager.add_download(download3)
        print(f"     Result: {result3}")
        
        # Try same filename in different path
        download4 = {
            'id': str(uuid.uuid4()),
            'url': 'https://example.com/another.zip',  # Different URL
            'filename': 'file1.zip',  # Same filename
            'save_path': '/downloads/subfolder',  # Different path
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  📂 Trying same filename in different path...")
        result4 = download_manager.add_download(download4)
        print(f"     Result: {result4}")
        
        download_manager.stop()
        
        print("\n  📊 Results:")
        print(f"     First download: {result1} (should be True)")
        print(f"     Same URL: {result2} (should be False)")
        print(f"     Same filename/path: {result3} (should be False)")
        print(f"     Same filename/different path: {result4} (should be True)")
        
        # Check if results match expectations
        expected = [True, False, False, True]
        actual = [result1, result2, result3, result4]
        
        if actual == expected:
            print("\n  ✅ All tests PASSED!")
            return True
        else:
            print(f"\n  ❌ Tests FAILED!")
            print(f"     Expected: {expected}")
            print(f"     Actual:   {actual}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_duplicate()
    if success:
        print("\n🎉 Duplicate detection is working!")
    else:
        print("\n💥 Duplicate detection needs fixing!")
