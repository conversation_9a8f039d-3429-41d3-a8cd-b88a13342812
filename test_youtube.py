#!/usr/bin/env python3
"""
Test YouTube download functionality
"""

import sys
from pathlib import Path
import uuid

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_youtube_download():
    """Test YouTube download"""
    print("🎬 Testing YouTube Download...")
    
    try:
        from src.downloaders.youtube_downloader import YouTubeDownloader
        from src.core.downloader import Downloader
        
        # Test URL (Rick Roll - safe and always available)
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        print(f"📹 Testing URL: {test_url}")
        
        # Test getting video info
        yt_downloader = YouTubeDownloader()
        video_info = yt_downloader.get_video_info(test_url)
        
        if video_info:
            print(f"✅ Video Info Retrieved:")
            print(f"   Title: {video_info.get('title', 'Unknown')}")
            print(f"   Duration: {video_info.get('duration', 'Unknown')} seconds")
            print(f"   View Count: {video_info.get('view_count', 'Unknown')}")
        else:
            print("❌ Failed to get video info")
            return False
        
        # Test getting available qualities
        qualities = yt_downloader.get_available_qualities(test_url)
        print(f"✅ Available Qualities: {qualities}")
        
        # Test download using Downloader class
        download_info = {
            'id': str(uuid.uuid4()),
            'url': test_url,
            'filename': 'test_video.mp4',
            'save_path': './downloads',
            'category': 'Videos',
            'video_type': 'youtube',
            'quality': 'worst',  # Use worst quality for faster test
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0
        }
        
        def progress_callback(progress):
            status = progress.get('status', 'unknown')
            if status == 'downloading':
                percent = progress.get('progress', 0)
                speed = progress.get('speed', 0)
                print(f"📥 Downloading: {percent:.1f}% - Speed: {speed/1024:.1f} KB/s")
            elif status == 'completed':
                print("✅ Download completed!")
        
        print("🚀 Starting download test...")
        downloader = Downloader(download_info, progress_callback)
        
        # Start download (this will take some time)
        success = downloader.start()
        
        if success:
            print("✅ YouTube download test PASSED!")
            return True
        else:
            print("❌ YouTube download test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_detection():
    """Test URL detection"""
    print("\n🔍 Testing URL Detection...")
    
    try:
        from src.utils.url_validator import URLValidator
        
        test_urls = [
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", "YouTube"),
            ("https://youtu.be/dQw4w9WgXcQ", "YouTube Short"),
            ("https://www.tiktok.com/@user/video/123", "TikTok"),
            ("https://example.com/file.mp4", "Direct File")
        ]
        
        for url, description in test_urls:
            is_youtube = URLValidator.is_youtube_url(url)
            is_tiktok = URLValidator.is_tiktok_url(url)
            is_valid = URLValidator.is_valid_url(url)
            
            print(f"  {description}: Valid={is_valid}, YouTube={is_youtube}, TikTok={is_tiktok}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL detection test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 YouTube Download Manager - Test Suite")
    print("=" * 60)
    
    # Test URL detection first
    url_test = test_url_detection()
    
    if url_test:
        print("\n" + "=" * 60)
        # Test actual download (commented out for safety)
        # Uncomment the line below to test actual download
        # youtube_test = test_youtube_download()
        
        print("\n✅ Basic tests completed successfully!")
        print("\n📋 To test actual download:")
        print("1. Uncomment the youtube_test line in main()")
        print("2. Run the script again")
        print("3. Or use the GUI to test downloads")
        
        print("\n🎯 GUI Testing Instructions:")
        print("1. Open the main application")
        print("2. Click 'إضافة تحميل' (Add Download)")
        print("3. Paste: https://www.youtube.com/watch?v=dQw4w9WgXcQ")
        print("4. Click 'تحليل' (Analyze)")
        print("5. Select quality and click 'إضافة للتحميل' (Add to Download)")
    else:
        print("❌ Basic tests failed!")

if __name__ == "__main__":
    main()
