"""
Configuration file for Python Download Manager
"""
import os
from pathlib import Path

# Application Info
APP_NAME = "Python Download Manager"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Python DM Team"

# Paths
BASE_DIR = Path(__file__).parent
DOWNLOADS_DIR = BASE_DIR / "downloads"
TEMP_DIR = BASE_DIR / "temp"
CONFIG_DIR = BASE_DIR / "config"
LOGS_DIR = BASE_DIR / "logs"

# Create directories if they don't exist
for directory in [DOWNLOADS_DIR, TEMP_DIR, CONFIG_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)

# Database
DATABASE_PATH = CONFIG_DIR / "downloads.db"

# Download Settings
DEFAULT_DOWNLOAD_PATH = str(DOWNLOADS_DIR)
MAX_CONCURRENT_DOWNLOADS = 5
DEFAULT_CHUNK_SIZE = 8192  # 8KB chunks
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds
AUTO_START_DOWNLOADS = True  # Start downloads immediately after adding
SKIP_SCHEDULE_DIALOG = True  # Skip schedule dialog for extension downloads
SHOW_DUPLICATE_DIALOG = True  # Show dialog for duplicate files
DEFAULT_DUPLICATE_ACTION = "ask"  # ask, rename, replace, skip

# Network Settings
DEFAULT_TIMEOUT = 30
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# GUI Settings
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 700
THEME = "dark"  # "light" or "dark"

# Video Download Settings
YOUTUBE_QUALITY_OPTIONS = [
    "best",
    "worst", 
    "720p",
    "480p",
    "360p",
    "240p",
    "144p"
]

TIKTOK_QUALITY_OPTIONS = [
    "best",
    "worst"
]

# Browser Extension Settings
EXTENSION_PORT = 9876
EXTENSION_HOST = "localhost"

# Logging
LOG_LEVEL = "INFO"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
LOG_ROTATION = "10 MB"
LOG_RETENTION = "1 week"

# File Types
SUPPORTED_VIDEO_FORMATS = [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm"]
SUPPORTED_AUDIO_FORMATS = [".mp3", ".wav", ".flac", ".aac", ".ogg", ".m4a"]
SUPPORTED_ARCHIVE_FORMATS = [".zip", ".rar", ".7z", ".tar", ".gz"]
SUPPORTED_DOCUMENT_FORMATS = [".pdf", ".doc", ".docx", ".txt", ".rtf"]

# Categories
FILE_CATEGORIES = {
    "Videos": SUPPORTED_VIDEO_FORMATS,
    "Audio": SUPPORTED_AUDIO_FORMATS,
    "Archives": SUPPORTED_ARCHIVE_FORMATS,
    "Documents": SUPPORTED_DOCUMENT_FORMATS,
    "Others": []
}

# Colors (for dark theme)
COLORS = {
    "primary": "#1f538d",
    "secondary": "#14375e", 
    "success": "#198754",
    "danger": "#dc3545",
    "warning": "#ffc107",
    "info": "#0dcaf0",
    "light": "#f8f9fa",
    "dark": "#212529",
    "background": "#2b2b2b",
    "surface": "#3c3c3c",
    "text": "#ffffff",
    "text_secondary": "#cccccc"
}
