/**
 * Microsoft Edge Background Service Worker
 * Python Download Manager Extension
 */

// إعدادات الخادم
const SERVER_URL = 'http://localhost:9876';

// تسجيل بدء الإضافة
console.log('🌊 Edge Extension: Python Download Manager started');

/**
 * معالج الرسائل من content script
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 Edge: Received message:', request.action);
    
    switch (request.action) {
        case 'download':
            handleDownload(request.videoInfo)
                .then(response => sendResponse(response))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // للاستجابة غير المتزامنة
            
        case 'getFormats':
            getVideoFormats(request.videoInfo)
                .then(response => sendResponse(response))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'checkServer':
            checkServerStatus()
                .then(response => sendResponse(response))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

/**
 * معالجة طلب التحميل
 */
async function handleDownload(videoInfo) {
    try {
        console.log('🎬 Edge: Starting download for:', videoInfo.title);
        
        const response = await fetch(`${SERVER_URL}/download`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: videoInfo.url,
                title: videoInfo.title,
                format_info: videoInfo.format_info || null
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('✅ Edge: Download response:', data);
        
        // إشعار Edge المحسن
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Python Download Manager',
                message: `تم إضافة "${videoInfo.title}" للتحميل`
            });
        }
        
        return {
            success: true,
            message: data.message || 'تم إضافة التحميل بنجاح',
            data: data
        };
        
    } catch (error) {
        console.error('❌ Edge: Download error:', error);
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return {
                success: false,
                error: 'البرنامج غير متصل. تأكد من تشغيل Python Download Manager'
            };
        }
        
        return {
            success: false,
            error: error.message || 'خطأ في التحميل'
        };
    }
}

/**
 * الحصول على صيغ الفيديو المتاحة
 */
async function getVideoFormats(videoInfo) {
    try {
        console.log('📊 Edge: Getting formats for:', videoInfo.url);
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout
        
        const response = await fetch(`${SERVER_URL}/formats`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: videoInfo.url
            }),
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`✅ Edge: Got ${data.formats?.length || 0} formats (${data.cached ? 'cached' : 'fresh'})`);
        
        return {
            success: true,
            formats: data.formats || [],
            cached: data.cached || false
        };
        
    } catch (error) {
        console.error('❌ Edge: Get formats error:', error);
        
        if (error.name === 'AbortError') {
            return {
                success: false,
                error: 'انتهت مهلة الطلب. حاول مرة أخرى'
            };
        }
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return {
                success: false,
                error: 'البرنامج غير متصل. تأكد من تشغيل Python Download Manager'
            };
        }
        
        return {
            success: false,
            error: error.message || 'خطأ في الحصول على الصيغ'
        };
    }
}

/**
 * فحص حالة الخادم
 */
async function checkServerStatus() {
    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch(`${SERVER_URL}/ping`, {
            method: 'GET',
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        return {
            success: true,
            status: data.status,
            message: data.message
        };
        
    } catch (error) {
        console.error('❌ Edge: Server check error:', error);
        
        return {
            success: false,
            error: 'الخادم غير متاح'
        };
    }
}

/**
 * معالج تحديث التبويب
 */
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // التحقق من تحميل صفحة YouTube
    if (changeInfo.status === 'complete' && tab.url && 
        (tab.url.includes('youtube.com') || tab.url.includes('youtu.be'))) {
        
        console.log('🎬 Edge: YouTube page loaded:', tab.url);
        
        // تحديث أيقونة الإضافة
        chrome.action.setBadgeText({
            text: '●',
            tabId: tabId
        });
        
        chrome.action.setBadgeBackgroundColor({
            color: '#4CAF50',
            tabId: tabId
        });
    }
});

/**
 * معالج تثبيت الإضافة
 */
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('🎉 Edge: Extension installed successfully');
        
        // إعداد السياق الافتراضي
        chrome.action.setBadgeText({ text: '' });
        
    } else if (details.reason === 'update') {
        console.log('🔄 Edge: Extension updated to version', chrome.runtime.getManifest().version);
    }
});

/**
 * معالج بدء تشغيل المتصفح
 */
chrome.runtime.onStartup.addListener(() => {
    console.log('🚀 Edge: Browser started, extension ready');
});

/**
 * معالج النقر على أيقونة الإضافة
 */
chrome.action.onClicked.addListener((tab) => {
    if (tab.url && (tab.url.includes('youtube.com') || tab.url.includes('youtu.be'))) {
        // إرسال رسالة لـ content script
        chrome.tabs.sendMessage(tab.id, { action: 'toggleDownloadIcon' });
    } else {
        // فتح صفحة YouTube
        chrome.tabs.create({ url: 'https://www.youtube.com' });
    }
});

// تصدير للاختبار
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        handleDownload,
        getVideoFormats,
        checkServerStatus
    };
}
