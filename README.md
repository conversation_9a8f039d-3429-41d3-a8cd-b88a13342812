# Python Download Manager

برنامج تحميل شامل يشبه IDM مع دعم تحميل الفيديوهات من YouTube و TikTok وإضافة للمتصفح.

## المميزات

### 🚀 المميزات الأساسية
- **تحميل متعدد الخيوط**: تحميل عدة ملفات في نفس الوقت
- **استكمال التحميل**: إمكانية إيقاف واستكمال التحميلات
- **واجهة مستخدم حديثة**: تصميم يشبه IDM مع دعم الثيم المظلم
- **إدارة قائمة التحميل**: تنظيم وتصنيف التحميلات
- **حفظ التقدم**: حفظ حالة التحميلات في قاعدة البيانات

### 🎬 دعم الفيديو
- **YouTube**: تحميل الفيديوهات بجودات مختلفة (4K, 1080p, 720p, إلخ)
- **TikTok**: تحميل فيديوهات TikTok
- **استخراج الصوت**: تحميل الصوت فقط من الفيديوهات
- **معلومات الفيديو**: عرض تفاصيل الفيديو قبل التحميل

### 🌐 إضافة المتصفح
- **Chrome/Firefox**: إضافة للمتصفح لتحميل مباشر
- **أزرار تحميل**: أزرار تحميل مدمجة في صفحات YouTube و TikTok
- **تحليل الروابط**: تحليل تلقائي للروابط وعرض الخيارات المتاحة
- **واجهة منبثقة**: نافذة منبثقة سهلة الاستخدام

## متطلبات النظام

- Python 3.8 أو أحدث
- Windows 10/11 (دعم أنظمة أخرى قريباً)
- 4 GB RAM على الأقل
- 1 GB مساحة فارغة

## التثبيت

### 1. تثبيت Python والمتطلبات

```bash
# استنساخ المشروع
git clone https://github.com/your-username/python-download-manager.git
cd python-download-manager

# إنشاء بيئة افتراضية
python -m venv venv
venv\Scripts\activate  # على Windows
# source venv/bin/activate  # على Linux/Mac

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. تشغيل البرنامج

```bash
python main.py
```

### 3. تثبيت إضافة المتصفح

#### Chrome:
1. افتح Chrome واذهب إلى `chrome://extensions/`
2. فعل "وضع المطور" (Developer mode)
3. انقر على "تحميل إضافة غير مُعبأة" (Load unpacked)
4. اختر مجلد `browser_extension`

#### Firefox:
1. افتح Firefox واذهب إلى `about:debugging`
2. انقر على "This Firefox"
3. انقر على "Load Temporary Add-on"
4. اختر ملف `manifest.json` من مجلد `browser_extension`

## الاستخدام

### الواجهة الرئيسية

1. **إضافة تحميل جديد**: انقر على "إضافة تحميل" وأدخل الرابط
2. **تحميل من الحافظة**: انقر على "إضافة من الحافظة" أو Ctrl+V
3. **إدارة التحميلات**: استخدم أزرار التحكم لكل تحميل
4. **الإعدادات**: اضبط مسار التحميل وعدد الاتصالات المتزامنة

### إضافة المتصفح

1. **التحميل المباشر**: انقر على زر "تحميل" في صفحات YouTube/TikTok
2. **النافذة المنبثقة**: انقر على أيقونة الإضافة في شريط الأدوات
3. **القائمة السياقية**: انقر بالزر الأيمن على الروابط واختر "تحميل باستخدام Python DM"

## هيكل المشروع

```
python_download/
├── main.py                 # نقطة دخول البرنامج
├── config.py              # إعدادات البرنامج
├── requirements.txt       # متطلبات Python
├── src/
│   ├── core/             # المحرك الأساسي
│   │   ├── download_manager.py
│   │   ├── downloader.py
│   │   ├── database.py
│   │   └── logger.py
│   ├── gui/              # واجهة المستخدم
│   │   ├── main_window.py
│   │   ├── add_download_dialog.py
│   │   ├── download_item.py
│   │   └── settings_dialog.py
│   ├── downloaders/      # محركات التحميل المتخصصة
│   │   ├── youtube_downloader.py
│   │   └── tiktok_downloader.py
│   ├── services/         # الخدمات
│   │   └── extension_server.py
│   └── utils/            # الأدوات المساعدة
│       ├── url_validator.py
│       └── file_utils.py
├── browser_extension/    # إضافة المتصفح
│   ├── manifest.json
│   ├── background.js
│   ├── content.js
│   ├── popup.html
│   ├── popup.js
│   └── popup.css
├── downloads/           # مجلد التحميلات الافتراضي
├── config/             # ملفات الإعدادات
├── logs/               # ملفات السجلات
└── temp/               # ملفات مؤقتة
```

## الإعدادات

يمكن تخصيص البرنامج من خلال:

- **واجهة الإعدادات**: من القائمة الرئيسية
- **ملف الإعدادات**: `config/settings.json`
- **متغيرات البيئة**: لإعدادات متقدمة

### إعدادات مهمة:

```json
{
  "download_path": "C:/Downloads",
  "max_concurrent_downloads": 5,
  "chunk_size": 8192,
  "theme": "dark",
  "auto_start": true
}
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **البرنامج لا يبدأ**:
   - تأكد من تثبيت Python 3.8+
   - تحقق من تثبيت جميع المتطلبات

2. **إضافة المتصفح لا تعمل**:
   - تأكد من تشغيل البرنامج الرئيسي
   - تحقق من أن المنفذ 9876 غير محجوب

3. **فشل تحميل YouTube/TikTok**:
   - تحديث yt-dlp: `pip install --upgrade yt-dlp`
   - تحقق من اتصال الإنترنت

### ملفات السجلات:

- `logs/download_manager.log`: سجل عام
- `logs/errors.log`: سجل الأخطاء
- `logs/downloads/`: سجلات التحميلات الفردية

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات مع اختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## الدعم

- **GitHub Issues**: لتقارير الأخطاء والاقتراحات
- **Wiki**: للوثائق المفصلة
- **Discussions**: للأسئلة والمناقشات

## خارطة الطريق

### الإصدار القادم (v1.1):
- [ ] دعم المزيد من المواقع
- [ ] جدولة التحميلات
- [ ] تحسينات الأداء
- [ ] دعم Linux/Mac

### المستقبل:
- [ ] واجهة ويب
- [ ] API للمطورين
- [ ] تطبيق موبايل
- [ ] تكامل مع خدمات التخزين السحابي

---

**ملاحظة**: هذا المشروع للاستخدام التعليمي والشخصي. يرجى احترام حقوق الطبع والنشر وشروط الخدمة للمواقع المختلفة.
