# تثبيت الإضافة في Microsoft Edge

## الخطوة 1: فتح صفحة الإضافات
1. افتح Microsoft Edge
2. اكتب في شريط العنوان: `edge://extensions/`
3. اضغط Enter

## الخطوة 2: تفعيل وضع المطور
1. اب<PERSON><PERSON> عن "Developer mode" في الجانب الأيسر
2. انقر على المفتاح لتفعيله ☑
3. ستظهر أزرار إضافية

## الخطوة 3: تحميل الإضافة
1. انقر على زر "Load unpacked"
2. ستفتح نافذة اختيار المجلد
3. انتقل إلى مجلد: `browser_extensions/edge/`
4. اختر المجلد كاملاً (ليس ملف منفرد)
5. انقر "Select Folder"

## الخطوة 4: التحقق من التثبيت
1. ستظهر الإضافة في قائمة الإضافات
2. ستجد: "🌊 Python Download Manager v1.0.0"
3. ستظهر علامة "Developer mode" تحت الاسم
4. ستظهر أيقونة الإضافة في شريط الأدوات

## الخطوة 5: تثبيت الأيقونة
1. انقر على أيقونة الإضافات في شريط الأدوات (⋯)
2. ابحث عن "Python Download Manager"
3. انقر على أيقونة "Pin" 📌 لتثبيتها

## ميزات Edge الخاصة:
- 🔔 إشعارات Windows متكاملة
- 🎯 أيقونة ملونة مميزة (🌊)
- ⚡ أداء محسن على Windows
- 🔄 تحديث تلقائي للحالة

## استكشاف الأخطاء:
- إذا لم تظهر الإضافة: تأكد من تفعيل Developer mode
- إذا ظهر خطأ: تحقق من وجود جميع ملفات الإضافة
- إذا لم تعمل: تأكد من تشغيل Python Download Manager
- للإزالة: انقر "Remove" في صفحة الإضافات
