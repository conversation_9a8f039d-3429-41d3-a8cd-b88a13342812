"""
Python Download Manager - Main Application Entry Point
A comprehensive download manager similar to IDM with YouTube and TikTok support
"""

import sys
import os
import tkinter as tk
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import application modules
from src.gui.main_window import MainWindow
from src.core.database import DatabaseManager
from src.core.logger import setup_logger, log_system_info
from src.services.extension_server import ExtensionServer
from src.core.scheduler import DownloadScheduler
from src.core.category_manager import CategoryManager
import config

def main():
    """Main application entry point"""
    try:
        # Setup logging
        logger = setup_logger()
        log_system_info()
        logger.info(f"Starting {config.APP_NAME} v{config.APP_VERSION}")

        # Initialize database
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        logger.info("Database initialized successfully")

        # Initialize category manager
        category_manager = CategoryManager()
        logger.info("Category manager initialized")

        # Initialize scheduler
        scheduler = DownloadScheduler()

        # Create and run GUI
        root = tk.Tk()
        app = MainWindow(root, scheduler=scheduler, category_manager=category_manager)

        # Set scheduler download manager reference
        scheduler.download_manager = app.download_manager
        scheduler.start()
        logger.info("Download scheduler started")

        # Start browser extension server
        extension_server = ExtensionServer()
        extension_server.start()
        logger.info(f"Extension server started on {config.EXTENSION_HOST}:{config.EXTENSION_PORT}")

        # Set extension server callback
        extension_server.set_download_callback(app.on_extension_download)

        logger.info("GUI initialized successfully")

        # Start the application
        root.mainloop()
        
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        try:
            if 'scheduler' in locals():
                scheduler.stop()
            if 'extension_server' in locals():
                extension_server.stop()
        except Exception as e:
            print(f"Error during cleanup: {e}")

if __name__ == "__main__":
    main()
