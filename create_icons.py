#!/usr/bin/env python3
"""
Create simple icons for the browser extension
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_download_icon(size, filename):
    """Create a simple download icon"""
    # Create a new image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    bg_color = (33, 150, 243, 255)  # Blue
    arrow_color = (255, 255, 255, 255)  # White
    
    # Draw background circle
    margin = 2
    draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color)
    
    # Draw download arrow
    center_x = size // 2
    center_y = size // 2
    arrow_size = size // 3
    
    # Arrow shaft
    shaft_width = max(2, size // 8)
    draw.rectangle([
        center_x - shaft_width//2, 
        center_y - arrow_size, 
        center_x + shaft_width//2, 
        center_y + arrow_size//3
    ], fill=arrow_color)
    
    # Arrow head (triangle)
    arrow_head_size = max(4, size // 4)
    points = [
        (center_x, center_y + arrow_size//2),  # Bottom point
        (center_x - arrow_head_size, center_y - arrow_size//4),  # Left point
        (center_x + arrow_head_size, center_y - arrow_size//4),  # Right point
    ]
    draw.polygon(points, fill=arrow_color)
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    """Create all required icon sizes"""
    icons_dir = "browser_extension/icons"
    
    # Create icons directory if it doesn't exist
    os.makedirs(icons_dir, exist_ok=True)
    
    # Icon sizes required by Chrome/Firefox
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = os.path.join(icons_dir, f"icon{size}.png")
        create_download_icon(size, filename)
    
    print("\n✅ All icons created successfully!")
    print("📁 Icons location: browser_extension/icons/")
    print("🔧 You can now load the extension in your browser")

if __name__ == "__main__":
    main()
