<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة المنسدلة للصيغ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-player {
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
        }
        
        .download-icon {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .download-icon:hover {
            background: rgba(33, 150, 243, 0.9);
            transform: scale(1.1);
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار القائمة المنسدلة للصيغ</h1>
        
        <div class="test-info">
            <h3>📋 تعليمات الاختبار:</h3>
            <ol>
                <li>انقر على أيقونة التحميل أسفل المشغل الوهمي</li>
                <li>ستظهر قائمة منسدلة بالصيغ المتاحة</li>
                <li>اختر أي صيغة لاختبار التحميل</li>
                <li>تحقق من أن القائمة تظهر في المكان الصحيح</li>
            </ol>
        </div>
        
        <div class="video-player">
            <div id="pdm-download-icon" class="download-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                </svg>
            </div>
            <div style="color: white; text-align: center; padding-top: 130px; font-size: 18px;">
                🎬 مشغل فيديو وهمي للاختبار
            </div>
        </div>
        
        <div>
            <button class="test-button" onclick="testDropdown()">🧪 اختبار القائمة المنسدلة</button>
            <button class="test-button" onclick="testWithManyFormats()">📊 اختبار مع صيغ متعددة</button>
            <button class="test-button" onclick="clearDropdown()">🗑️ إزالة القائمة</button>
        </div>
        
        <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; display: none;">
            <h4>📊 نتائج الاختبار:</h4>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        // Mock video info
        const mockVideoInfo = {
            title: "فيديو تجريبي للاختبار",
            url: "https://www.youtube.com/watch?v=test123"
        };

        // Mock formats data
        const mockFormats = [
            {
                format_id: "137",
                ext: "mp4",
                quality: "1080p",
                height: 1080,
                width: 1920,
                filesize: 118500000,
                fps: 30,
                vcodec: "avc1.640028",
                acodec: "none"
            },
            {
                format_id: "136",
                ext: "mp4", 
                quality: "720p",
                height: 720,
                width: 1280,
                filesize: 61390000,
                fps: 30,
                vcodec: "avc1.64001f",
                acodec: "none"
            },
            {
                format_id: "135",
                ext: "mp4",
                quality: "480p", 
                height: 480,
                width: 854,
                filesize: 31440000,
                fps: 30,
                vcodec: "avc1.4d401e",
                acodec: "none"
            },
            {
                format_id: "134",
                ext: "mp4",
                quality: "360p",
                height: 360,
                width: 640,
                filesize: 14790000,
                fps: 30,
                vcodec: "avc1.4d401e",
                acodec: "none"
            },
            {
                format_id: "140",
                ext: "m4a",
                quality: "صوت فقط",
                height: 0,
                filesize: 4620000,
                acodec: "mp4a.40.2",
                vcodec: "none"
            }
        ];

        // Format file size function
        function formatFileSize(bytes) {
            if (!bytes) return 'غير معروف';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }

        // Show format dropdown (copied from content.js)
        function showFormatDialog(formats, videoInfo) {
            // Remove existing dropdown if any
            const existingDropdown = document.getElementById('pdm-format-dropdown');
            if (existingDropdown) {
                existingDropdown.remove();
            }
            
            // Get download icon position
            const downloadIcon = document.getElementById('pdm-download-icon');
            if (!downloadIcon) {
                console.error('Download icon not found');
                return;
            }
            
            const iconRect = downloadIcon.getBoundingClientRect();
            
            // Create dropdown container
            const dropdown = document.createElement('div');
            dropdown.id = 'pdm-format-dropdown';
            dropdown.style.cssText = `
                position: fixed;
                top: ${iconRect.bottom + 8}px;
                left: ${iconRect.left}px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                min-width: 300px;
                max-width: 400px;
                max-height: 400px;
                overflow-y: auto;
                font-family: Arial, sans-serif;
                border: 1px solid #ddd;
            `;
            
            // Header
            const header = document.createElement('div');
            header.style.cssText = `
                padding: 12px 16px;
                border-bottom: 1px solid #eee;
                background: #f8f9fa;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            `;
            
            const title = document.createElement('div');
            title.textContent = 'اختر صيغة التحميل';
            title.style.cssText = `
                font-weight: 600;
                color: #333;
                font-size: 14px;
            `;
            
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '✕';
            closeBtn.style.cssText = `
                background: none;
                border: none;
                font-size: 16px;
                cursor: pointer;
                color: #666;
                padding: 2px 6px;
                border-radius: 3px;
                line-height: 1;
            `;
            closeBtn.onclick = () => {
                dropdown.remove();
                resetDownloadIcon();
            };
            
            header.appendChild(title);
            header.appendChild(closeBtn);
            
            // Format list
            const formatList = document.createElement('div');
            formatList.style.cssText = `
                max-height: 320px;
                overflow-y: auto;
            `;
            
            // Add formats to dropdown
            formats.forEach((format, index) => {
                const formatItem = document.createElement('div');
                formatItem.style.cssText = `
                    padding: 10px 16px;
                    cursor: pointer;
                    transition: background 0.2s;
                    border-bottom: 1px solid #f0f0f0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                `;
                
                const formatInfo = document.createElement('div');
                const isAudio = !format.height || format.height === 0;
                const qualityLabel = isAudio ? 'صوت فقط' : format.quality || `${format.height}p`;
                
                // Get quality icon
                const getQualityIcon = (format) => {
                    if (isAudio) return '🎵';
                    const height = format.height || 0;
                    if (height >= 2160) return '🎬'; // 4K
                    if (height >= 1080) return '📺'; // 1080p
                    if (height >= 720) return '🎥'; // 720p
                    return '📱'; // Lower quality
                };
                
                formatInfo.innerHTML = `
                    <div style="display: flex; align-items: center; margin-bottom: 2px;">
                        <span style="margin-right: 6px; font-size: 14px;">${getQualityIcon(format)}</span>
                        <span style="font-weight: 500; color: #333; font-size: 13px;">
                            ${qualityLabel} - ${format.ext || 'mp4'}
                        </span>
                    </div>
                    <div style="font-size: 11px; color: #666; margin-left: 20px;">
                        ${format.filesize ? formatFileSize(format.filesize) : 'حجم غير معروف'}
                        ${format.fps && !isAudio ? ` • ${format.fps}fps` : ''}
                    </div>
                `;
                
                const downloadIcon = document.createElement('div');
                downloadIcon.innerHTML = '⬇️';
                downloadIcon.style.cssText = `
                    font-size: 16px;
                    opacity: 0.7;
                    transition: opacity 0.2s;
                `;
                
                formatItem.onmouseover = () => {
                    formatItem.style.background = '#f0f8ff';
                    downloadIcon.style.opacity = '1';
                };
                
                formatItem.onmouseout = () => {
                    formatItem.style.background = 'white';
                    downloadIcon.style.opacity = '0.7';
                };
                
                formatItem.onclick = () => {
                    downloadWithFormat(videoInfo, format);
                    dropdown.remove();
                };
                
                formatItem.appendChild(formatInfo);
                formatItem.appendChild(downloadIcon);
                formatList.appendChild(formatItem);
            });
            
            // Assemble dropdown
            dropdown.appendChild(header);
            dropdown.appendChild(formatList);
            document.body.appendChild(dropdown);
            
            // Close dropdown when clicking outside
            const closeDropdown = (e) => {
                if (!dropdown.contains(e.target) && e.target !== downloadIcon) {
                    dropdown.remove();
                    resetDownloadIcon();
                    document.removeEventListener('click', closeDropdown);
                }
            };
            
            setTimeout(() => {
                document.addEventListener('click', closeDropdown);
            }, 100);
        }

        function resetDownloadIcon() {
            const icon = document.getElementById('pdm-download-icon');
            if (icon) {
                icon.style.transform = 'scale(1)';
                icon.style.background = 'rgba(0, 0, 0, 0.8)';
            }
        }

        function downloadWithFormat(videoInfo, format) {
            const results = document.getElementById('test-results');
            const content = document.getElementById('results-content');
            
            content.innerHTML = `
                <p><strong>✅ تم اختيار الصيغة بنجاح!</strong></p>
                <p><strong>الفيديو:</strong> ${videoInfo.title}</p>
                <p><strong>الصيغة:</strong> ${format.quality} - ${format.ext}</p>
                <p><strong>الحجم:</strong> ${formatFileSize(format.filesize)}</p>
                <p><strong>معرف الصيغة:</strong> ${format.format_id}</p>
                <p style="color: #4CAF50;">في التطبيق الحقيقي، سيبدأ التحميل الآن!</p>
            `;
            
            results.style.display = 'block';
            
            // Reset icon
            resetDownloadIcon();
        }

        function testDropdown() {
            showFormatDialog(mockFormats, mockVideoInfo);
        }

        function testWithManyFormats() {
            const manyFormats = [
                ...mockFormats,
                {format_id: "313", ext: "webm", quality: "4K (2160p)", height: 2160, filesize: 305340000, fps: 24},
                {format_id: "271", ext: "webm", quality: "1440p", height: 1440, filesize: 175320000, fps: 24},
                {format_id: "248", ext: "webm", quality: "1080p", height: 1080, filesize: 49320000, fps: 24}
            ];
            showFormatDialog(manyFormats, mockVideoInfo);
        }

        function clearDropdown() {
            const dropdown = document.getElementById('pdm-format-dropdown');
            if (dropdown) {
                dropdown.remove();
            }
            resetDownloadIcon();
            
            const results = document.getElementById('test-results');
            results.style.display = 'none';
        }

        // Add click handler to download icon
        document.getElementById('pdm-download-icon').onclick = testDropdown;
    </script>
</body>
</html>
