#!/usr/bin/env python3
"""
اختبار إصلاح أسماء الملفات
"""

import requests
import json

SERVER_URL = "http://localhost:9876"

def test_filename_extraction():
    """اختبار استخراج أسماء الملفات الصحيحة"""
    print("🧪 اختبار استخراج أسماء الملفات الصحيحة")
    print("=" * 60)
    
    # فيديوهات للاختبار
    test_videos = [
        {
            "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "expected_title": "<PERSON>tley - Never Gonna Give You Up",
            "description": "فيديو Rick Roll الشهير"
        },
        {
            "url": "https://www.youtube.com/watch?v=TRHym5jkLtQ", 
            "expected_title": "The city of Batna",
            "description": "فيديو مدينة باتنة"
        }
    ]
    
    for i, video in enumerate(test_videos, 1):
        print(f"\n🎬 اختبار {i}: {video['description']}")
        print(f"📋 الرابط: {video['url']}")
        
        try:
            # محاكاة طلب من الإضافة مع العنوان
            download_data = {
                "url": video['url'],
                "title": video['expected_title'],
                "format_info": {
                    "format_id": "18",
                    "ext": "mp4",
                    "quality": "360p"
                }
            }
            
            print(f"📤 إرسال البيانات:")
            print(f"   📝 العنوان: {download_data['title']}")
            print(f"   🎯 الصيغة: {download_data['format_info']['quality']}")
            
            # إرسال طلب التحميل (محاكاة فقط)
            response = requests.post(
                f"{SERVER_URL}/download",
                json=download_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ نجح الطلب!")
                    print(f"📄 اسم الملف المتوقع: {video['expected_title']}.mp4")
                    
                    # التحقق من الرسالة
                    message = data.get('message', '')
                    if 'already in queue' in message.lower():
                        print(f"⚠️ الملف موجود في القائمة مسبقاً")
                        print(f"💡 هذا يعني أن النظام يتذكر الملفات بأسمائها الصحيحة")
                    else:
                        print(f"📨 رسالة الخادم: {message}")
                else:
                    print(f"❌ فشل الطلب: {data.get('error', 'خطأ غير معروف')}")
            else:
                print(f"❌ خطأ HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")

def test_title_cleaning():
    """اختبار تنظيف العناوين"""
    print("\n🧹 اختبار تنظيف العناوين")
    print("=" * 60)
    
    # عناوين للاختبار مع رموز مشكلة
    test_titles = [
        {
            "original": "Video: Test <Title> with \"Quotes\" and /Slashes\\",
            "description": "عنوان مع رموز مشكلة"
        },
        {
            "original": "Very Long Title That Exceeds The Normal Length Limit And Should Be Truncated To Avoid Filesystem Issues And Problems",
            "description": "عنوان طويل جداً"
        },
        {
            "original": "Title   with    Multiple     Spaces",
            "description": "عنوان مع مسافات متعددة"
        },
        {
            "original": "عنوان بالعربية - Arabic Title",
            "description": "عنوان مختلط عربي وإنجليزي"
        }
    ]
    
    for i, test in enumerate(test_titles, 1):
        print(f"\n🧪 اختبار {i}: {test['description']}")
        print(f"📝 العنوان الأصلي: {test['original']}")
        
        try:
            # محاكاة طلب مع عنوان مشكل
            download_data = {
                "url": "https://www.youtube.com/watch?v=test123",
                "title": test['original'],
                "format_info": {
                    "format_id": "18",
                    "ext": "mp4"
                }
            }
            
            # إرسال طلب (محاكاة فقط - لن نحمل فعلياً)
            print(f"📤 إرسال العنوان للخادم...")
            
            # هنا يمكن إضافة طلب فعلي للاختبار
            # response = requests.post(f"{SERVER_URL}/download", json=download_data)
            
            # محاكاة التنظيف المتوقع
            import html
            clean_title = html.unescape(test['original'].strip())
            invalid_chars = '<>:"/\\|?*'
            for char in invalid_chars:
                clean_title = clean_title.replace(char, '')
            clean_title = ' '.join(clean_title.split())
            if len(clean_title) > 100:
                clean_title = clean_title[:100].rstrip()
            
            expected_filename = f"{clean_title}.mp4"
            print(f"✅ اسم الملف المتوقع: {expected_filename}")
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    print("🔌 اختبار الاتصال بالخادم...")
    
    try:
        response = requests.get(f"{SERVER_URL}/ping", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok':
                print("✅ الخادم متصل ويعمل")
                return True
            else:
                print(f"⚠️ الخادم يرد لكن بحالة غير طبيعية: {data}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح أسماء الملفات")
    print("=" * 70)
    
    # اختبار الاتصال أولاً
    if not test_server_connection():
        print("\n❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل البرنامج أولاً.")
        return
    
    # اختبار استخراج أسماء الملفات
    test_filename_extraction()
    
    # اختبار تنظيف العناوين
    test_title_cleaning()
    
    print("\n🎉 اكتمل اختبار أسماء الملفات!")
    print("\n💡 النتائج المتوقعة:")
    print("- ✅ أسماء الملفات تعكس عناوين الفيديوهات الحقيقية")
    print("- ✅ الرموز المشكلة تُزال من أسماء الملفات")
    print("- ✅ العناوين الطويلة تُقصر لتجنب مشاكل النظام")
    print("- ✅ المسافات المتعددة تُنظف")
    print("- ✅ النظام يتذكر الملفات بأسمائها الصحيحة")
    
    print("\n🔄 للاختبار الفعلي:")
    print("1. أعد تحميل الإضافة في المتصفح")
    print("2. اذهب إلى فيديو YouTube")
    print("3. حمل الفيديو وتحقق من اسم الملف")
    print("4. جرب تحميل نفس الفيديو مرة أخرى")
    print("5. يجب أن يخبرك أنه موجود مسبقاً بالاسم الصحيح")

if __name__ == "__main__":
    main()
