# Development dependencies for Python Download Manager

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-asyncio>=0.21.0

# Code quality
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
isort>=5.12.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# Building
pyinstaller>=5.13.0
auto-py-to-exe>=2.34.0
cx-Freeze>=6.15.0

# Development tools
pre-commit>=3.3.0
tox>=4.6.0
coverage>=7.2.0

# Debugging
pdb++>=0.10.3
ipython>=8.14.0
ipdb>=0.13.13

# Profiling
memory-profiler>=0.61.0
line-profiler>=4.1.0

# Security
bandit>=1.7.5
safety>=2.3.0

# Type checking
types-requests>=2.31.0
types-Pillow>=10.0.0
