# تثبيت الإضافة في Chromium

## الخطوة 1: فتح صفحة الإضافات
1. افتح Chromium
2. اكتب في شريط العنوان: `chrome://extensions/`
3. اضغط Enter

## الخطوة 2: تفعيل وضع المطور
1. ابح<PERSON> عن "Developer mode" في الزاوية العلوية اليمنى
2. انقر على المفتاح لتفعيله ☑
3. ستظهر أزرار إضافية في الأعلى

## الخطوة 3: تحميل الإضافة
1. انقر على زر "Load unpacked" في الأعلى
2. ستفتح نافذة اختيار المجلد
3. انتقل إلى مجلد: `browser_extensions/chromium/`
4. اختر المجلد كاملاً
5. انقر "Select Folder"

## الخطوة 4: التحقق من التثبيت
1. ستظهر الإضافة في قائمة الإضافات
2. ستجد: "🌐 Python Download Manager v1.0.0"
3. ستظهر معلومات "Developer mode"
4. ستظهر أيقونة الإضافة في شريط الأدوات

## الخطوة 5: إدارة الإضافة
1. انقر على "Details" لمزيد من الخيارات
2. يمكنك تفعيل/إلغاء تفعيل الإضافة
3. يمكنك إزالتها بالنقر على "Remove"
4. يمكنك إعادة تحميلها بالنقر على "Reload"

## ميزات Chromium الخاصة:
- 🌐 مفتوح المصدر وقابل للتخصيص
- 🚀 أداء سريع مع محرك Blink
- 🔧 مرونة في التطوير والاختبار
- 📊 إحصائيات مفصلة للأداء

## نصائح للاستخدام:
- 🔄 أعد تحميل الإضافة بعد أي تحديث
- 🎯 استخدم "Inspect views" لفحص الأخطاء
- 📝 تحقق من "Console" للرسائل التشخيصية
- 🛡️ احتفظ بنسخة احتياطية من مجلد الإضافة

## استكشاف الأخطاء:
- إذا لم تظهر الإضافة: تأكد من تفعيل Developer mode
- إذا ظهر خطأ تحميل: تحقق من صحة ملف manifest.json
- إذا لم تعمل الوظائف: تأكد من تشغيل الخادم على localhost:9876
- للتحديث: احذف الإضافة وأعد تحميلها
