"""
Build script for Python Download Manager
Creates executable files for different platforms
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import platform

def run_command(command, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error: {e.stderr}")
        return None

def clean_build():
    """Clean previous build artifacts"""
    print("🧹 Cleaning previous build artifacts...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   Removed {dir_name}")
    
    # Remove .pyc files
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith(".pyc"):
                os.remove(os.path.join(root, file))

def install_build_dependencies():
    """Install build dependencies"""
    print("📦 Installing build dependencies...")
    
    dependencies = [
        "pyinstaller>=5.0.0",
        "auto-py-to-exe>=2.0.0"
    ]
    
    for dep in dependencies:
        print(f"   Installing {dep}...")
        result = run_command(f"pip install {dep}")
        if result is None:
            print(f"   ❌ Failed to install {dep}")
            return False
    
    print("   ✅ Build dependencies installed")
    return True

def build_executable():
    """Build executable using PyInstaller"""
    print("🔨 Building executable...")
    
    # Determine platform-specific settings
    system = platform.system()
    
    # Base PyInstaller command
    cmd_parts = [
        "pyinstaller",
        "--onefile",
        "--name=PythonDownloadManager",
        "--distpath=dist",
        "--workpath=build",
        "--specpath=build"
    ]
    
    # Add platform-specific options
    if system == "Windows":
        cmd_parts.extend([
            "--windowed",
            "--icon=icon.ico"
        ])
    elif system == "Darwin":  # macOS
        cmd_parts.extend([
            "--windowed",
            "--icon=icon.icns"
        ])
    else:  # Linux
        cmd_parts.append("--console")
    
    # Add hidden imports
    hidden_imports = [
        "tkinter",
        "customtkinter",
        "PIL",
        "requests",
        "yt_dlp",
        "flask",
        "sqlite3"
    ]
    
    for import_name in hidden_imports:
        cmd_parts.extend(["--hidden-import", import_name])
    
    # Add data files
    data_files = [
        ("browser_extension", "browser_extension"),
        ("config", "config"),
        ("README.md", "."),
        ("LICENSE", ".")
    ]
    
    for src, dst in data_files:
        if os.path.exists(src):
            cmd_parts.extend(["--add-data", f"{src}{os.pathsep}{dst}"])
    
    # Add main script
    cmd_parts.append("main.py")
    
    # Run PyInstaller
    command = " ".join(cmd_parts)
    print(f"   Running: {command}")
    
    result = run_command(command)
    if result is None:
        print("   ❌ Build failed")
        return False
    
    print("   ✅ Executable built successfully")
    return True

def create_installer():
    """Create installer package"""
    print("📦 Creating installer package...")
    
    system = platform.system()
    
    if system == "Windows":
        # Create NSIS installer script (if NSIS is available)
        nsis_script = create_nsis_script()
        if nsis_script and shutil.which("makensis"):
            result = run_command(f"makensis {nsis_script}")
            if result:
                print("   ✅ Windows installer created")
            else:
                print("   ⚠️ Windows installer creation failed")
        else:
            print("   ⚠️ NSIS not found, skipping Windows installer")
    
    elif system == "Darwin":
        # Create macOS app bundle and DMG
        create_macos_bundle()
    
    elif system == "Linux":
        # Create AppImage or DEB package
        create_linux_package()

def create_nsis_script():
    """Create NSIS installer script for Windows"""
    script_content = '''
!define APP_NAME "Python Download Manager"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Python DM Team"
!define APP_EXE "PythonDownloadManager.exe"

Name "${APP_NAME}"
OutFile "PythonDownloadManager-Setup.exe"
InstallDir "$PROGRAMFILES\\${APP_NAME}"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Install"
    SetOutPath "$INSTDIR"
    File "dist\\${APP_EXE}"
    File /r "browser_extension"
    
    CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    CreateShortCut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\${APP_EXE}"
    
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\${APP_EXE}"
    Delete "$INSTDIR\\uninstall.exe"
    RMDir /r "$INSTDIR\\browser_extension"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk"
    RMDir "$SMPROGRAMS\\${APP_NAME}"
    Delete "$DESKTOP\\${APP_NAME}.lnk"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}"
SectionEnd
'''
    
    script_path = "installer.nsi"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    return script_path

def create_macos_bundle():
    """Create macOS app bundle"""
    print("   Creating macOS app bundle...")
    # Implementation for macOS bundle creation
    print("   ⚠️ macOS bundle creation not implemented yet")

def create_linux_package():
    """Create Linux package"""
    print("   Creating Linux package...")
    # Implementation for Linux package creation
    print("   ⚠️ Linux package creation not implemented yet")

def package_browser_extension():
    """Package browser extension"""
    print("🌐 Packaging browser extension...")
    
    # Create ZIP file for Chrome Web Store
    import zipfile
    
    extension_files = []
    extension_dir = Path("browser_extension")
    
    for file_path in extension_dir.rglob("*"):
        if file_path.is_file() and not file_path.name.startswith('.'):
            extension_files.append(file_path)
    
    zip_path = "dist/browser_extension.zip"
    os.makedirs("dist", exist_ok=True)
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in extension_files:
            arcname = file_path.relative_to(extension_dir)
            zipf.write(file_path, arcname)
    
    print(f"   ✅ Browser extension packaged: {zip_path}")

def main():
    """Main build function"""
    print("🚀 Python Download Manager - Build Script")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or newer is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"🖥️  Platform: {platform.system()} {platform.machine()}")
    
    # Clean previous builds
    clean_build()
    
    # Install build dependencies
    if not install_build_dependencies():
        sys.exit(1)
    
    # Build executable
    if not build_executable():
        sys.exit(1)
    
    # Package browser extension
    package_browser_extension()
    
    # Create installer
    create_installer()
    
    print("\n" + "=" * 50)
    print("✅ Build completed successfully!")
    print(f"📁 Output directory: {os.path.abspath('dist')}")
    
    # List built files
    if os.path.exists("dist"):
        print("\n📋 Built files:")
        for file in os.listdir("dist"):
            file_path = os.path.join("dist", file)
            size = os.path.getsize(file_path) if os.path.isfile(file_path) else 0
            size_mb = size / (1024 * 1024)
            print(f"   {file} ({size_mb:.1f} MB)")

if __name__ == "__main__":
    main()
