/* Content styles for Python Download Manager extension */

/* Download icon overlay styles */
.pdm-download-icon {
    position: absolute !important;
    top: 15px !important;
    left: 15px !important;
    z-index: 1000 !important;
    cursor: pointer !important;
    background: rgba(0, 0, 0, 0.8) !important;
    border-radius: 50% !important;
    padding: 12px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4) !important;
    opacity: 0.9 !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(4px) !important;
}

.pdm-download-icon:hover {
    background: rgba(33, 150, 243, 0.9) !important;
    transform: scale(1.1) !important;
    opacity: 1 !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.5) !important;
}

.pdm-download-icon:active {
    transform: scale(0.95) !important;
}

.pdm-download-icon svg {
    display: block !important;
    width: 32px !important;
    height: 32px !important;
    fill: white !important;
    transition: all 0.3s ease !important;
}

/* Success state */
.pdm-download-icon.success {
    background: rgba(76, 175, 80, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.6) !important;
}

/* Error state */
.pdm-download-icon.error {
    background: rgba(244, 67, 54, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.6) !important;
}

/* Notification styles */
#pdm-notification {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: bold !important;
    z-index: 10000 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
    max-width: 350px !important;
    text-align: center !important;
    color: white !important;
    backdrop-filter: blur(8px) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Animation keyframes */
@keyframes slideInRight {
    from { 
        transform: translateX(100%) !important; 
        opacity: 0 !important; 
    }
    to { 
        transform: translateX(0) !important; 
        opacity: 1 !important; 
    }
}

@keyframes slideOutRight {
    from { 
        transform: translateX(0) !important; 
        opacity: 1 !important; 
    }
    to { 
        transform: translateX(100%) !important; 
        opacity: 0 !important; 
    }
}

/* Pulse animation for icon */
@keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4); }
    50% { box-shadow: 0 4px 16px rgba(33, 150, 243, 0.6); }
    100% { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4); }
}

.pdm-download-icon.pulse {
    animation: pulse 2s infinite !important;
}

/* Icon appearance animation */
@keyframes iconAppear {
    from {
        opacity: 0 !important;
        transform: scale(0.5) !important;
    }
    to {
        opacity: 0.9 !important;
        transform: scale(1) !important;
    }
}

.pdm-download-icon {
    animation: iconAppear 0.4s ease-out !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pdm-download-icon {
        top: 10px !important;
        left: 10px !important;
        padding: 10px !important;
    }
    
    .pdm-download-icon svg {
        width: 28px !important;
        height: 28px !important;
    }
    
    #pdm-notification {
        top: 10px !important;
        right: 10px !important;
        max-width: 280px !important;
        font-size: 13px !important;
        padding: 12px 20px !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .pdm-download-icon {
        background: rgba(30, 30, 30, 0.9) !important;
        border-color: rgba(255, 255, 255, 0.3) !important;
    }
    
    .pdm-download-icon:hover {
        background: rgba(33, 150, 243, 0.9) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .pdm-download-icon {
        background: rgba(0, 0, 0, 0.95) !important;
        border: 3px solid white !important;
    }
    
    .pdm-download-icon:hover {
        background: rgba(33, 150, 243, 0.95) !important;
        border-color: white !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .pdm-download-icon {
        transition: none !important;
        animation: none !important;
    }
    
    .pdm-download-icon:hover {
        transform: none !important;
    }
    
    #pdm-notification {
        animation: none !important;
    }
    
    .pdm-download-icon.pulse {
        animation: none !important;
    }
}
