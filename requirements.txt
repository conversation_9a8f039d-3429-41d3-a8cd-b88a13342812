# Core dependencies
requests>=2.31.0
urllib3>=2.0.0
certifi>=2023.7.22

# GUI Framework
tkinter-tooltip>=2.2.0
pillow>=10.0.0
customtkinter>=5.2.0

# Video downloading
yt-dlp>=2023.10.13
pytube>=15.0.0

# Progress tracking and threading
tqdm>=4.66.0
threading-timer>=0.1.0

# File handling and utilities
pathlib>=1.0.1
json5>=0.9.14
configparser>=6.0.0

# Network and HTTP
aiohttp>=3.8.6
httpx>=0.25.0

# System integration
psutil>=5.9.6
win32api>=0.0.0; sys_platform == "win32"
pywin32>=306; sys_platform == "win32"

# Browser extension communication
websockets>=11.0.3
flask>=2.3.3
flask-cors>=4.0.0

# Media processing
ffmpeg-python>=0.2.0

# Database for download history
sqlite3

# Encryption and security
cryptography>=41.0.7

# Logging
loguru>=0.7.2
