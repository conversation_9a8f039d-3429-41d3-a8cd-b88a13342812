// Popup script for Python Download Manager extension

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const statusDiv = document.getElementById('status');
    const mainContent = document.getElementById('mainContent');
    const errorContent = document.getElementById('errorContent');
    const urlInput = document.getElementById('urlInput');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const downloadAudioBtn = document.getElementById('downloadAudioBtn');
    const currentPageBtn = document.getElementById('currentPageBtn');
    const retryBtn = document.getElementById('retryBtn');
    const qualitySelect = document.getElementById('qualitySelect');
    const categorySelect = document.getElementById('categorySelect');
    const videoInfo = document.getElementById('videoInfo');
    
    let currentTab = null;
    let currentVideoInfo = null;
    
    // Initialize popup
    init();
    
    async function init() {
        try {
            // Get current tab
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            currentTab = tabs[0];
            
            // Check server connection
            await checkServerConnection();
            
            // Auto-fill URL if on supported site
            if (isSupportedSite(currentTab.url)) {
                urlInput.value = currentTab.url;
                analyzeUrl(currentTab.url);
            }
            
            showMainContent();
            
        } catch (error) {
            console.error('Initialization error:', error);
            showErrorContent();
        }
    }
    
    async function checkServerConnection() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'ping' }, (response) => {
                if (response && response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response?.error || 'Server not responding'));
                }
            });
        });
    }
    
    function isSupportedSite(url) {
        return url.includes('youtube.com') || 
               url.includes('youtu.be') || 
               url.includes('tiktok.com');
    }
    
    function showMainContent() {
        statusDiv.style.display = 'none';
        errorContent.style.display = 'none';
        mainContent.style.display = 'block';
        mainContent.classList.add('fade-in');
    }
    
    function showErrorContent() {
        statusDiv.style.display = 'none';
        mainContent.style.display = 'none';
        errorContent.style.display = 'block';
        errorContent.classList.add('fade-in');
    }
    
    function showStatus(message) {
        statusDiv.innerHTML = `<div class="loading">${message}</div>`;
        statusDiv.style.display = 'block';
        mainContent.style.display = 'none';
        errorContent.style.display = 'none';
    }
    
    async function analyzeUrl(url) {
        if (!url || !isValidUrl(url)) {
            hideVideoInfo();
            return;
        }
        
        try {
            analyzeBtn.textContent = 'جاري التحليل...';
            analyzeBtn.disabled = true;
            
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ 
                    action: 'analyze', 
                    url: url 
                }, (response) => {
                    if (response && response.success) {
                        resolve(response.data);
                    } else {
                        reject(new Error(response?.error || 'Analysis failed'));
                    }
                });
            });
            
            currentVideoInfo = response;
            displayVideoInfo(response);
            updateQualityOptions(response.available_qualities);
            updateCategorySelection(response.platform);
            
        } catch (error) {
            console.error('Analysis error:', error);
            hideVideoInfo();
            showMessage('فشل في تحليل الرابط', 'warning');
        } finally {
            analyzeBtn.textContent = 'تحليل';
            analyzeBtn.disabled = false;
        }
    }
    
    function displayVideoInfo(info) {
        if (info.title || info.uploader || info.duration) {
            document.getElementById('videoTitle').textContent = info.title || 'غير محدد';
            document.getElementById('videoDuration').textContent = info.duration ? formatDuration(info.duration) : 'غير محدد';
            document.getElementById('videoUploader').textContent = info.uploader || 'غير محدد';
            
            videoInfo.style.display = 'block';
        } else {
            hideVideoInfo();
        }
    }
    
    function hideVideoInfo() {
        videoInfo.style.display = 'none';
        currentVideoInfo = null;
    }
    
    function updateQualityOptions(qualities) {
        if (qualities && qualities.length > 0) {
            qualitySelect.innerHTML = '';
            qualities.forEach(quality => {
                const option = document.createElement('option');
                option.value = quality;
                option.textContent = getQualityLabel(quality);
                qualitySelect.appendChild(option);
            });
        }
    }
    
    function updateCategorySelection(platform) {
        if (platform === 'youtube' || platform === 'tiktok') {
            categorySelect.value = 'Videos';
        }
    }
    
    function getQualityLabel(quality) {
        const labels = {
            'best': 'أفضل جودة',
            'worst': 'أقل جودة',
            '4K': '4K (2160p)',
            '1440p': '1440p (2K)',
            '1080p': '1080p (Full HD)',
            '720p': '720p (HD)',
            '480p': '480p',
            '360p': '360p',
            '240p': '240p',
            '144p': '144p'
        };
        return labels[quality] || quality;
    }
    
    function formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    function isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
    
    async function downloadFile(options = {}) {
        const url = urlInput.value.trim();
        
        if (!url) {
            showMessage('يرجى إدخال رابط صالح', 'warning');
            return;
        }
        
        if (!isValidUrl(url)) {
            showMessage('الرابط المدخل غير صالح', 'warning');
            return;
        }
        
        try {
            downloadBtn.disabled = true;
            downloadAudioBtn.disabled = true;
            downloadBtn.innerHTML = '<div class="loading"></div> جاري التحميل...';
            
            const downloadOptions = {
                quality: qualitySelect.value,
                category: categorySelect.value,
                ...options
            };
            
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'download',
                    url: url,
                    options: downloadOptions
                }, (response) => {
                    if (response && response.success) {
                        resolve(response.data);
                    } else {
                        reject(new Error(response?.error || 'Download failed'));
                    }
                });
            });
            
            showMessage('تم إضافة التحميل بنجاح!', 'success');
            
            // Close popup after successful download
            setTimeout(() => {
                window.close();
            }, 1500);
            
        } catch (error) {
            console.error('Download error:', error);
            showMessage(`فشل في إضافة التحميل: ${error.message}`, 'error');
        } finally {
            downloadBtn.disabled = false;
            downloadAudioBtn.disabled = false;
            downloadBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                </svg>
                تحميل
            `;
        }
    }
    
    function showMessage(message, type = 'info') {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.success-message, .warning-message, .error-message');
        existingMessages.forEach(msg => msg.remove());
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message`;
        
        let icon = '';
        if (type === 'success') {
            icon = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>';
        } else if (type === 'warning') {
            icon = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>';
        } else if (type === 'error') {
            icon = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>';
        }
        
        messageDiv.innerHTML = `${icon}<span>${message}</span>`;
        
        // Insert at the top of main content
        mainContent.insertBefore(messageDiv, mainContent.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }
    
    // Event listeners
    analyzeBtn.addEventListener('click', () => {
        const url = urlInput.value.trim();
        if (url) {
            analyzeUrl(url);
        }
    });
    
    urlInput.addEventListener('input', () => {
        const url = urlInput.value.trim();
        if (url && isValidUrl(url)) {
            // Auto-analyze after a short delay
            clearTimeout(urlInput.timeout);
            urlInput.timeout = setTimeout(() => {
                analyzeUrl(url);
            }, 1000);
        } else {
            hideVideoInfo();
        }
    });
    
    downloadBtn.addEventListener('click', () => {
        downloadFile();
    });
    
    downloadAudioBtn.addEventListener('click', () => {
        downloadFile({ audioOnly: true });
    });
    
    currentPageBtn.addEventListener('click', () => {
        if (currentTab && currentTab.url) {
            urlInput.value = currentTab.url;
            analyzeUrl(currentTab.url);
        }
    });
    
    retryBtn.addEventListener('click', () => {
        init();
    });
    
    // Settings and about links
    document.getElementById('settingsLink').addEventListener('click', (e) => {
        e.preventDefault();
        // Open settings page or show settings dialog
        showMessage('الإعدادات متاحة في التطبيق الرئيسي', 'info');
    });
    
    document.getElementById('aboutLink').addEventListener('click', (e) => {
        e.preventDefault();
        showMessage('Python Download Manager v1.0.0', 'info');
    });
});
