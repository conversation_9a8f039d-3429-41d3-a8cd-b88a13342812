<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل Python Download Manager</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
        }
        
        .message {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .steps {
            text-align: right;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
        
        .command {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }
        
        .buttons {
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🚀</div>
        <h1>تشغيل Python Download Manager</h1>
        
        <div class="message">
            البرنامج غير مُشغل حالياً. يرجى اتباع الخطوات التالية لتشغيله:
        </div>
        
        <div class="steps">
            <div class="step">
                <span class="step-number">1</span>
                افتح Command Prompt أو Terminal
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                انتقل إلى مجلد البرنامج:
                <div class="command">cd C:\Users\<USER>\Desktop\augment\python_download</div>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                شغل البرنامج:
                <div class="command">python main.py</div>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                انتظر حتى ترى رسالة "GUI initialized successfully"
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                ارجع إلى YouTube وجرب الإضافة مرة أخرى
            </div>
        </div>
        
        <div class="buttons">
            <button class="btn" onclick="checkServer()">فحص حالة البرنامج</button>
            <button class="btn btn-secondary" onclick="openFolder()">فتح مجلد البرنامج</button>
            <button class="btn btn-secondary" onclick="window.close()">إغلاق</button>
        </div>
        
        <div id="status" class="status"></div>
    </div>

    <script>
        async function checkServer() {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            statusDiv.textContent = 'جاري فحص حالة البرنامج...';
            statusDiv.className = 'status';
            
            try {
                const response = await fetch('http://localhost:9876/ping');
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.textContent = '✅ البرنامج يعمل بنجاح! يمكنك الآن استخدام الإضافة.';
                    statusDiv.className = 'status success';
                    
                    // Close this tab after 3 seconds
                    setTimeout(() => {
                        window.close();
                    }, 3000);
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                statusDiv.textContent = '❌ البرنامج لا يزال غير مُشغل. يرجى اتباع الخطوات أعلاه.';
                statusDiv.className = 'status error';
            }
        }
        
        function openFolder() {
            // Try to open the folder (limited by browser security)
            try {
                window.open('file:///C:/Users/<USER>/Desktop/augment/python_download/', '_blank');
            } catch (error) {
                alert('لا يمكن فتح المجلد تلقائياً. يرجى الانتقال يدوياً إلى:\nC:\\Users\\<USER>\\Desktop\\augment\\python_download');
            }
        }
        
        // Auto-check server status when page loads
        window.addEventListener('load', () => {
            setTimeout(checkServer, 1000);
        });
        
        // Auto-refresh every 5 seconds
        setInterval(checkServer, 5000);
    </script>
</body>
</html>
