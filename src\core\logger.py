"""
Logging system for Python Download Manager
"""

import logging
import sys
from pathlib import Path
from logging.handlers import RotatingFileHandler
import os

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

# Global logger instance
_logger = None

def setup_logger():
    """Setup and configure the main logger"""
    global _logger
    
    if _logger is not None:
        return _logger
    
    # Create logger
    _logger = logging.getLogger(config.APP_NAME)
    _logger.setLevel(getattr(logging, config.LOG_LEVEL.upper(), logging.INFO))
    
    # Clear any existing handlers
    _logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler with rotation
    log_file = config.LOGS_DIR / "download_manager.log"
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    _logger.addHandler(file_handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    _logger.addHandler(console_handler)
    
    # Error file handler
    error_log_file = config.LOGS_DIR / "errors.log"
    error_handler = RotatingFileHandler(
        error_log_file,
        maxBytes=5*1024*1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    _logger.addHandler(error_handler)
    
    return _logger

def get_logger(name=None):
    """Get a logger instance"""
    if _logger is None:
        setup_logger()
    
    if name:
        return logging.getLogger(f"{config.APP_NAME}.{name}")
    else:
        return _logger

class DownloadLogger:
    """Specialized logger for download operations"""
    
    def __init__(self, download_id, filename):
        self.download_id = download_id
        self.filename = filename
        self.logger = get_logger(f"download.{download_id[:8]}")
        
        # Create download-specific log file
        self.log_file = config.LOGS_DIR / "downloads" / f"{download_id}.log"
        self.log_file.parent.mkdir(exist_ok=True)
        
        # Add file handler for this download
        self.file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        self.file_handler.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self.file_handler.setFormatter(formatter)
        self.logger.addHandler(self.file_handler)
    
    def info(self, message):
        """Log info message"""
        self.logger.info(f"[{self.filename}] {message}")
    
    def error(self, message):
        """Log error message"""
        self.logger.error(f"[{self.filename}] {message}")
    
    def warning(self, message):
        """Log warning message"""
        self.logger.warning(f"[{self.filename}] {message}")
    
    def debug(self, message):
        """Log debug message"""
        self.logger.debug(f"[{self.filename}] {message}")
    
    def progress(self, progress, speed, downloaded, total):
        """Log progress information"""
        if total > 0:
            percent = (downloaded / total) * 100
            self.logger.info(
                f"[{self.filename}] Progress: {percent:.1f}% "
                f"({self.format_size(downloaded)}/{self.format_size(total)}) "
                f"Speed: {self.format_speed(speed)}"
            )
        else:
            self.logger.info(
                f"[{self.filename}] Downloaded: {self.format_size(downloaded)} "
                f"Speed: {self.format_speed(speed)}"
            )
    
    def completed(self, total_time, average_speed, file_size):
        """Log completion information"""
        self.logger.info(
            f"[{self.filename}] Download completed! "
            f"Time: {self.format_time(total_time)} "
            f"Size: {self.format_size(file_size)} "
            f"Avg Speed: {self.format_speed(average_speed)}"
        )
    
    def failed(self, error_message):
        """Log failure information"""
        self.logger.error(f"[{self.filename}] Download failed: {error_message}")
    
    def cleanup(self):
        """Cleanup logger resources"""
        if self.file_handler:
            self.logger.removeHandler(self.file_handler)
            self.file_handler.close()
    
    @staticmethod
    def format_size(bytes_size):
        """Format file size in human readable format"""
        if bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.1f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.1f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.1f} GB"
    
    @staticmethod
    def format_speed(bytes_per_second):
        """Format speed in human readable format"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.1f} B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"
    
    @staticmethod
    def format_time(seconds):
        """Format time in human readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}m"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}h"

def log_system_info():
    """Log system information at startup"""
    logger = get_logger()
    
    logger.info(f"Starting {config.APP_NAME} v{config.APP_VERSION}")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Platform: {sys.platform}")
    logger.info(f"Working directory: {os.getcwd()}")
    logger.info(f"Config directory: {config.CONFIG_DIR}")
    logger.info(f"Downloads directory: {config.DOWNLOADS_DIR}")
    logger.info(f"Logs directory: {config.LOGS_DIR}")

def log_download_start(download_info):
    """Log download start"""
    logger = get_logger()
    logger.info(
        f"Starting download: {download_info['filename']} "
        f"from {download_info['url'][:50]}..."
    )

def log_download_complete(download_info, duration):
    """Log download completion"""
    logger = get_logger()
    size = download_info.get('size', 0)
    speed = size / duration if duration > 0 else 0
    
    logger.info(
        f"Download completed: {download_info['filename']} "
        f"({DownloadLogger.format_size(size)}) "
        f"in {DownloadLogger.format_time(duration)} "
        f"at {DownloadLogger.format_speed(speed)}"
    )

def log_download_error(download_info, error):
    """Log download error"""
    logger = get_logger()
    logger.error(
        f"Download failed: {download_info['filename']} "
        f"Error: {str(error)}"
    )
