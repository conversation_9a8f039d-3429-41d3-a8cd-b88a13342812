"""
Download Scheduler for Python Download Manager
Handles scheduled downloads and queue management
"""

import threading
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
import sys
from typing import List, Dict, Optional
import uuid

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger

class ScheduledDownload:
    def __init__(self, download_info: Dict, scheduled_time: datetime, repeat_type: str = "none"):
        self.id = str(uuid.uuid4())
        self.download_info = download_info
        self.scheduled_time = scheduled_time
        self.repeat_type = repeat_type  # "none", "daily", "weekly", "monthly"
        self.created_at = datetime.now()
        self.is_active = True
        self.last_run = None
        self.next_run = scheduled_time

class DownloadScheduler:
    def __init__(self, download_manager=None):
        self.download_manager = download_manager
        self.scheduled_downloads: List[ScheduledDownload] = []
        self.scheduler_thread = None
        self.running = False
        self.logger = get_logger(__name__)
        
        # Load scheduled downloads from file
        self.load_scheduled_downloads()
    
    def start(self):
        """Start the scheduler"""
        if self.running:
            return
        
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("Download scheduler started")
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        self.logger.info("Download scheduler stopped")
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                current_time = datetime.now()
                
                # Check for downloads to execute
                for scheduled_download in self.scheduled_downloads[:]:  # Copy list to avoid modification during iteration
                    if not scheduled_download.is_active:
                        continue
                    
                    if current_time >= scheduled_download.next_run:
                        self._execute_scheduled_download(scheduled_download)
                
                # Sleep for 30 seconds before next check
                time.sleep(30)
                
            except Exception as e:
                self.logger.error(f"Scheduler loop error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _execute_scheduled_download(self, scheduled_download: ScheduledDownload):
        """Execute a scheduled download"""
        try:
            self.logger.info(f"Executing scheduled download: {scheduled_download.download_info['filename']}")
            
            # Add download to manager
            if self.download_manager:
                self.download_manager.add_download(scheduled_download.download_info)
            
            # Update last run time
            scheduled_download.last_run = datetime.now()
            
            # Calculate next run time based on repeat type
            if scheduled_download.repeat_type == "none":
                scheduled_download.is_active = False
            elif scheduled_download.repeat_type == "daily":
                scheduled_download.next_run = scheduled_download.next_run + timedelta(days=1)
            elif scheduled_download.repeat_type == "weekly":
                scheduled_download.next_run = scheduled_download.next_run + timedelta(weeks=1)
            elif scheduled_download.repeat_type == "monthly":
                # Add one month (approximate)
                scheduled_download.next_run = scheduled_download.next_run + timedelta(days=30)
            
            # Save updated schedule
            self.save_scheduled_downloads()
            
        except Exception as e:
            self.logger.error(f"Error executing scheduled download: {e}")
    
    def schedule_download(self, download_info: Dict, scheduled_time: datetime, repeat_type: str = "none") -> str:
        """Schedule a download"""
        try:
            scheduled_download = ScheduledDownload(download_info, scheduled_time, repeat_type)
            self.scheduled_downloads.append(scheduled_download)
            
            self.save_scheduled_downloads()
            
            self.logger.info(f"Download scheduled: {download_info['filename']} at {scheduled_time}")
            return scheduled_download.id
            
        except Exception as e:
            self.logger.error(f"Error scheduling download: {e}")
            return None
    
    def cancel_scheduled_download(self, schedule_id: str) -> bool:
        """Cancel a scheduled download"""
        try:
            for scheduled_download in self.scheduled_downloads:
                if scheduled_download.id == schedule_id:
                    scheduled_download.is_active = False
                    self.save_scheduled_downloads()
                    self.logger.info(f"Cancelled scheduled download: {schedule_id}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error cancelling scheduled download: {e}")
            return False
    
    def remove_scheduled_download(self, schedule_id: str) -> bool:
        """Remove a scheduled download"""
        try:
            for i, scheduled_download in enumerate(self.scheduled_downloads):
                if scheduled_download.id == schedule_id:
                    del self.scheduled_downloads[i]
                    self.save_scheduled_downloads()
                    self.logger.info(f"Removed scheduled download: {schedule_id}")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error removing scheduled download: {e}")
            return False
    
    def get_scheduled_downloads(self) -> List[Dict]:
        """Get all scheduled downloads"""
        result = []
        for scheduled_download in self.scheduled_downloads:
            result.append({
                'id': scheduled_download.id,
                'download_info': scheduled_download.download_info,
                'scheduled_time': scheduled_download.scheduled_time.isoformat(),
                'repeat_type': scheduled_download.repeat_type,
                'is_active': scheduled_download.is_active,
                'last_run': scheduled_download.last_run.isoformat() if scheduled_download.last_run else None,
                'next_run': scheduled_download.next_run.isoformat(),
                'created_at': scheduled_download.created_at.isoformat()
            })
        return result
    
    def get_upcoming_downloads(self, hours: int = 24) -> List[Dict]:
        """Get downloads scheduled in the next X hours"""
        current_time = datetime.now()
        end_time = current_time + timedelta(hours=hours)
        
        upcoming = []
        for scheduled_download in self.scheduled_downloads:
            if (scheduled_download.is_active and 
                current_time <= scheduled_download.next_run <= end_time):
                upcoming.append({
                    'id': scheduled_download.id,
                    'filename': scheduled_download.download_info['filename'],
                    'url': scheduled_download.download_info['url'],
                    'scheduled_time': scheduled_download.next_run.isoformat(),
                    'repeat_type': scheduled_download.repeat_type
                })
        
        # Sort by scheduled time
        upcoming.sort(key=lambda x: x['scheduled_time'])
        return upcoming
    
    def save_scheduled_downloads(self):
        """Save scheduled downloads to file"""
        try:
            schedule_file = config.CONFIG_DIR / "scheduled_downloads.json"
            
            data = []
            for scheduled_download in self.scheduled_downloads:
                data.append({
                    'id': scheduled_download.id,
                    'download_info': scheduled_download.download_info,
                    'scheduled_time': scheduled_download.scheduled_time.isoformat(),
                    'repeat_type': scheduled_download.repeat_type,
                    'is_active': scheduled_download.is_active,
                    'last_run': scheduled_download.last_run.isoformat() if scheduled_download.last_run else None,
                    'next_run': scheduled_download.next_run.isoformat(),
                    'created_at': scheduled_download.created_at.isoformat()
                })
            
            with open(schedule_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Error saving scheduled downloads: {e}")
    
    def load_scheduled_downloads(self):
        """Load scheduled downloads from file"""
        try:
            schedule_file = config.CONFIG_DIR / "scheduled_downloads.json"
            
            if not schedule_file.exists():
                return
            
            with open(schedule_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.scheduled_downloads = []
            for item in data:
                scheduled_download = ScheduledDownload(
                    download_info=item['download_info'],
                    scheduled_time=datetime.fromisoformat(item['scheduled_time']),
                    repeat_type=item['repeat_type']
                )
                
                scheduled_download.id = item['id']
                scheduled_download.is_active = item['is_active']
                scheduled_download.created_at = datetime.fromisoformat(item['created_at'])
                scheduled_download.next_run = datetime.fromisoformat(item['next_run'])
                
                if item['last_run']:
                    scheduled_download.last_run = datetime.fromisoformat(item['last_run'])
                
                self.scheduled_downloads.append(scheduled_download)
            
            self.logger.info(f"Loaded {len(self.scheduled_downloads)} scheduled downloads")
            
        except Exception as e:
            self.logger.error(f"Error loading scheduled downloads: {e}")
    
    def cleanup_old_schedules(self, days: int = 30):
        """Remove old completed schedules"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            before_count = len(self.scheduled_downloads)
            
            self.scheduled_downloads = [
                sd for sd in self.scheduled_downloads
                if sd.is_active or (sd.last_run and sd.last_run > cutoff_date)
            ]
            
            after_count = len(self.scheduled_downloads)
            removed_count = before_count - after_count
            
            if removed_count > 0:
                self.save_scheduled_downloads()
                self.logger.info(f"Cleaned up {removed_count} old scheduled downloads")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old schedules: {e}")
    
    def pause_all_schedules(self):
        """Pause all active schedules"""
        for scheduled_download in self.scheduled_downloads:
            if scheduled_download.is_active:
                scheduled_download.is_active = False
        
        self.save_scheduled_downloads()
        self.logger.info("All schedules paused")
    
    def resume_all_schedules(self):
        """Resume all paused schedules"""
        for scheduled_download in self.scheduled_downloads:
            if not scheduled_download.is_active:
                scheduled_download.is_active = True
        
        self.save_scheduled_downloads()
        self.logger.info("All schedules resumed")
