"""
Schedule Manager Dialog for Python Download Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

class ScheduleManagerDialog:
    def __init__(self, parent, scheduler):
        self.parent = parent
        self.scheduler = scheduler
        
        # Create dialog window
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("إدارة الجدولة")
        self.dialog.geometry("800x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.create_widgets()
        self.refresh_schedule_list()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Header
        header_frame = ctk.CTkFrame(main_frame)
        header_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(header_frame, text="إدارة التحميلات المجدولة", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=15)
        
        # Toolbar
        toolbar_frame = ctk.CTkFrame(main_frame)
        toolbar_frame.pack(fill="x", pady=(0, 10))
        
        self.refresh_btn = ctk.CTkButton(
            toolbar_frame,
            text="تحديث",
            command=self.refresh_schedule_list,
            width=80
        )
        self.refresh_btn.pack(side="left", padx=10, pady=10)
        
        self.pause_all_btn = ctk.CTkButton(
            toolbar_frame,
            text="إيقاف الكل",
            command=self.pause_all_schedules,
            width=100
        )
        self.pause_all_btn.pack(side="left", padx=5, pady=10)
        
        self.resume_all_btn = ctk.CTkButton(
            toolbar_frame,
            text="استكمال الكل",
            command=self.resume_all_schedules,
            width=100
        )
        self.resume_all_btn.pack(side="left", padx=5, pady=10)
        
        self.cleanup_btn = ctk.CTkButton(
            toolbar_frame,
            text="تنظيف القديم",
            command=self.cleanup_old_schedules,
            width=100
        )
        self.cleanup_btn.pack(side="right", padx=10, pady=10)
        
        # Schedule list
        list_frame = ctk.CTkFrame(main_frame)
        list_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # Create treeview for schedule list
        self.tree_frame = ctk.CTkFrame(list_frame)
        self.tree_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # Create treeview
        columns = ("filename", "scheduled_time", "repeat", "status", "last_run")
        self.tree = ttk.Treeview(self.tree_frame, columns=columns, show="headings", height=15)
        
        # Define headings
        self.tree.heading("filename", text="اسم الملف")
        self.tree.heading("scheduled_time", text="الوقت المجدول")
        self.tree.heading("repeat", text="التكرار")
        self.tree.heading("status", text="الحالة")
        self.tree.heading("last_run", text="آخر تشغيل")
        
        # Configure column widths
        self.tree.column("filename", width=200)
        self.tree.column("scheduled_time", width=150)
        self.tree.column("repeat", width=100)
        self.tree.column("status", width=80)
        self.tree.column("last_run", width=150)
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.tree_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind double-click event
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
        # Context menu
        self.create_context_menu()
        self.tree.bind("<Button-3>", self.show_context_menu)
        
        # Control buttons
        control_frame = ctk.CTkFrame(main_frame)
        control_frame.pack(fill="x")
        
        self.close_btn = ctk.CTkButton(
            control_frame,
            text="إغلاق",
            command=self.close_dialog,
            width=100
        )
        self.close_btn.pack(side="right", padx=15, pady=15)
        
        self.edit_btn = ctk.CTkButton(
            control_frame,
            text="تعديل",
            command=self.edit_selected_schedule,
            width=100
        )
        self.edit_btn.pack(side="right", padx=5, pady=15)
        
        self.delete_btn = ctk.CTkButton(
            control_frame,
            text="حذف",
            command=self.delete_selected_schedule,
            width=100
        )
        self.delete_btn.pack(side="right", padx=5, pady=15)
        
        self.toggle_btn = ctk.CTkButton(
            control_frame,
            text="إيقاف/تشغيل",
            command=self.toggle_selected_schedule,
            width=120
        )
        self.toggle_btn.pack(side="right", padx=5, pady=15)
    
    def create_context_menu(self):
        """Create context menu for treeview"""
        self.context_menu = tk.Menu(self.dialog, tearoff=0)
        self.context_menu.add_command(label="تشغيل الآن", command=self.run_now)
        self.context_menu.add_command(label="تعديل", command=self.edit_selected_schedule)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="إيقاف/تشغيل", command=self.toggle_selected_schedule)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="حذف", command=self.delete_selected_schedule)
    
    def show_context_menu(self, event):
        """Show context menu"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if item:
            self.context_menu.post(event.x_root, event.y_root)
    
    def refresh_schedule_list(self):
        """Refresh the schedule list"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Get scheduled downloads
        scheduled_downloads = self.scheduler.get_scheduled_downloads()
        
        # Add items to tree
        for schedule in scheduled_downloads:
            # Format data
            filename = schedule['download_info']['filename']
            if len(filename) > 30:
                filename = filename[:27] + "..."
            
            scheduled_time = datetime.fromisoformat(schedule['scheduled_time']).strftime("%Y-%m-%d %H:%M")
            
            repeat_map = {
                "none": "لا يتكرر",
                "daily": "يومياً",
                "weekly": "أسبوعياً",
                "monthly": "شهرياً"
            }
            repeat = repeat_map.get(schedule['repeat_type'], schedule['repeat_type'])
            
            status = "نشط" if schedule['is_active'] else "متوقف"
            
            last_run = ""
            if schedule['last_run']:
                last_run = datetime.fromisoformat(schedule['last_run']).strftime("%Y-%m-%d %H:%M")
            
            # Insert item
            item_id = self.tree.insert("", "end", values=(filename, scheduled_time, repeat, status, last_run))
            
            # Store schedule ID in item
            self.tree.set(item_id, "schedule_id", schedule['id'])
    
    def get_selected_schedule_id(self):
        """Get selected schedule ID"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        # Get schedule ID from item data
        for schedule in self.scheduler.get_scheduled_downloads():
            item_values = self.tree.item(item)['values']
            if (schedule['download_info']['filename'].startswith(item_values[0]) or 
                item_values[0] in schedule['download_info']['filename']):
                return schedule['id']
        return None
    
    def on_item_double_click(self, event):
        """Handle double-click on item"""
        self.edit_selected_schedule()
    
    def edit_selected_schedule(self):
        """Edit selected schedule"""
        schedule_id = self.get_selected_schedule_id()
        if not schedule_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جدولة للتعديل")
            return
        
        # Find schedule
        for schedule in self.scheduler.get_scheduled_downloads():
            if schedule['id'] == schedule_id:
                # Open edit dialog (implement if needed)
                messagebox.showinfo("معلومات", "ميزة التعديل ستكون متاحة قريباً")
                break
    
    def delete_selected_schedule(self):
        """Delete selected schedule"""
        schedule_id = self.get_selected_schedule_id()
        if not schedule_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جدولة للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل تريد حذف هذه الجدولة؟"):
            if self.scheduler.remove_scheduled_download(schedule_id):
                messagebox.showinfo("نجح", "تم حذف الجدولة بنجاح")
                self.refresh_schedule_list()
            else:
                messagebox.showerror("خطأ", "فشل في حذف الجدولة")
    
    def toggle_selected_schedule(self):
        """Toggle selected schedule active state"""
        schedule_id = self.get_selected_schedule_id()
        if not schedule_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جدولة")
            return
        
        # Find and toggle schedule
        for schedule in self.scheduler.get_scheduled_downloads():
            if schedule['id'] == schedule_id:
                if schedule['is_active']:
                    self.scheduler.cancel_scheduled_download(schedule_id)
                    messagebox.showinfo("نجح", "تم إيقاف الجدولة")
                else:
                    # Reactivate (implement if needed)
                    messagebox.showinfo("معلومات", "ميزة إعادة التشغيل ستكون متاحة قريباً")
                
                self.refresh_schedule_list()
                break
    
    def run_now(self):
        """Run selected schedule now"""
        schedule_id = self.get_selected_schedule_id()
        if not schedule_id:
            messagebox.showwarning("تحذير", "يرجى اختيار جدولة")
            return
        
        # Find schedule and execute
        for schedule in self.scheduler.get_scheduled_downloads():
            if schedule['id'] == schedule_id:
                # Execute download immediately
                if self.scheduler.download_manager:
                    self.scheduler.download_manager.add_download(schedule['download_info'])
                    messagebox.showinfo("نجح", "تم بدء التحميل")
                else:
                    messagebox.showerror("خطأ", "مدير التحميل غير متاح")
                break
    
    def pause_all_schedules(self):
        """Pause all schedules"""
        if messagebox.askyesno("تأكيد", "هل تريد إيقاف جميع الجدولات؟"):
            self.scheduler.pause_all_schedules()
            messagebox.showinfo("نجح", "تم إيقاف جميع الجدولات")
            self.refresh_schedule_list()
    
    def resume_all_schedules(self):
        """Resume all schedules"""
        if messagebox.askyesno("تأكيد", "هل تريد استكمال جميع الجدولات؟"):
            self.scheduler.resume_all_schedules()
            messagebox.showinfo("نجح", "تم استكمال جميع الجدولات")
            self.refresh_schedule_list()
    
    def cleanup_old_schedules(self):
        """Cleanup old schedules"""
        if messagebox.askyesno("تأكيد", "هل تريد حذف الجدولات القديمة المكتملة؟"):
            self.scheduler.cleanup_old_schedules()
            messagebox.showinfo("نجح", "تم تنظيف الجدولات القديمة")
            self.refresh_schedule_list()
    
    def close_dialog(self):
        """Close dialog"""
        self.dialog.destroy()
