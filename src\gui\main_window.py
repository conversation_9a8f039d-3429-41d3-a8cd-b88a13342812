"""
Main Window for Python Download Manager
IDM-like interface with modern design
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from PIL import Image, ImageTk
import threading
import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.download_manager import DownloadManager
from src.gui.add_download_dialog import AddDownloadDialog
from src.gui.settings_dialog import SettingsDialog
from src.gui.download_item import DownloadItem
from src.gui.schedule_dialog import ScheduleDialog
from src.gui.schedule_manager_dialog import ScheduleManagerDialog
from src.gui.category_manager_dialog import CategoryManagerDialog
from src.core.logger import get_logger

class MainWindow:
    def __init__(self, root, scheduler=None, category_manager=None):
        self.root = root
        self.download_manager = DownloadManager(gui_parent=root)
        self.scheduler = scheduler
        self.category_manager = category_manager
        self.download_items = {}
        self.logger = get_logger(__name__)
        
        # Configure CustomTkinter
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.setup_window()
        self.create_menu()
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
        
        # Start download manager
        self.download_manager.start()

        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Start periodic status updates
        self.update_status_periodically()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title(f"{config.APP_NAME} v{config.APP_VERSION}")
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        self.root.minsize(800, 600)
        
        # Center window on screen
        self.center_window()
        
        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_menu(self):
        """Create application menu bar"""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إضافة تحميل جديد", command=self.add_download, accelerator="Ctrl+N")
        file_menu.add_command(label="إضافة من الحافظة", command=self.add_from_clipboard, accelerator="Ctrl+V")
        file_menu.add_separator()
        file_menu.add_command(label="إعدادات", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.on_closing)
        
        # Downloads menu
        downloads_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="التحميلات", menu=downloads_menu)
        downloads_menu.add_command(label="بدء الكل", command=self.start_all_downloads)
        downloads_menu.add_command(label="إيقاف الكل", command=self.pause_all_downloads)
        downloads_menu.add_command(label="حذف المكتملة", command=self.remove_completed)
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="تحميل فيديو YouTube", command=self.download_youtube)
        tools_menu.add_command(label="تحميل فيديو TikTok", command=self.download_tiktok)
        tools_menu.add_separator()
        tools_menu.add_command(label="إدارة الجدولة", command=self.show_schedule_manager)
        tools_menu.add_command(label="إدارة التصنيفات", command=self.show_category_manager)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-n>', lambda e: self.add_download())
        self.root.bind('<Control-v>', lambda e: self.add_from_clipboard())
    
    def create_toolbar(self):
        """Create toolbar with main action buttons"""
        self.toolbar_frame = ctk.CTkFrame(self.root)
        self.toolbar_frame.pack(fill="x", padx=5, pady=5)
        
        # Add download button
        self.add_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="إضافة تحميل",
            command=self.add_download,
            width=120
        )
        self.add_btn.pack(side="left", padx=5)
        
        # Start all button
        self.start_all_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="بدء الكل",
            command=self.start_all_downloads,
            width=100
        )
        self.start_all_btn.pack(side="left", padx=5)
        
        # Pause all button
        self.pause_all_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="إيقاف الكل",
            command=self.pause_all_downloads,
            width=100
        )
        self.pause_all_btn.pack(side="left", padx=5)
        
        # Remove completed button
        self.remove_completed_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="حذف المكتملة",
            command=self.remove_completed,
            width=120
        )
        self.remove_completed_btn.pack(side="left", padx=5)
        
        # Settings button
        self.settings_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="الإعدادات",
            command=self.show_settings,
            width=100
        )
        self.settings_btn.pack(side="right", padx=5)
    
    def create_main_content(self):
        """Create main content area with download list"""
        # Main content frame
        self.content_frame = ctk.CTkFrame(self.root)
        self.content_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Downloads list frame with scrollbar
        self.downloads_frame = ctk.CTkScrollableFrame(
            self.content_frame,
            label_text="قائمة التحميلات"
        )
        self.downloads_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Empty state label
        self.empty_label = ctk.CTkLabel(
            self.downloads_frame,
            text="لا توجد تحميلات حالياً\nانقر على 'إضافة تحميل' لبدء تحميل جديد",
            font=ctk.CTkFont(size=16)
        )
        self.empty_label.pack(expand=True)
    
    def create_status_bar(self):
        """Create status bar at bottom"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.pack(fill="x", side="bottom")
        self.status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="جاهز",
            anchor="w"
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Speed and downloads info
        self.info_label = ctk.CTkLabel(
            self.status_frame,
            text="التحميلات: 0 | السرعة: 0 KB/s",
            anchor="e"
        )
        self.info_label.pack(side="right", padx=10, pady=5)
    
    def add_download(self):
        """Show add download dialog"""
        dialog = AddDownloadDialog(self.root, self.on_download_added)
    
    def add_from_clipboard(self):
        """Add download from clipboard URL"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text and (clipboard_text.startswith('http') or clipboard_text.startswith('https')):
                dialog = AddDownloadDialog(self.root, self.on_download_added, url=clipboard_text)
            else:
                messagebox.showwarning("تحذير", "لا يوجد رابط صالح في الحافظة")
        except tk.TclError:
            messagebox.showwarning("تحذير", "لا يمكن قراءة محتوى الحافظة")
    
    def _create_progress_callback(self, download_id):
        """Create progress callback for download"""
        def callback(progress_info):
            # Update download item if it exists
            if download_id in self.download_items:
                item = self.download_items[download_id]
                item.update_progress(progress_info)
        return callback

    def add_new_download(self, download_info):
        """Add a new download with duplicate checking"""
        # Try to add download to manager
        success = self.download_manager.add_download(download_info, self._create_progress_callback(download_info['id']))

        if not success:
            # Download was rejected (duplicate or error)
            messagebox.showwarning(
                "تحذير",
                f"لا يمكن إضافة التحميل:\n{download_info['filename']}\n\nالسبب: الملف موجود بالفعل في قائمة الانتظار أو قيد التحميل"
            )
            return False

        # Create UI for the download
        self.on_download_added(download_info)
        return True

    def on_download_added(self, download_info):
        """Handle new download addition (UI only)"""
        # Hide empty label if visible
        if self.empty_label.winfo_viewable():
            self.empty_label.pack_forget()

        # Create download item widget
        download_item = DownloadItem(self.downloads_frame, download_info, self.download_manager)
        download_item.pack(fill="x", padx=5, pady=2)

        # Store reference
        self.download_items[download_info['id']] = download_item

        # Start download immediately if auto-start is enabled
        if config.AUTO_START_DOWNLOADS:
            download_item.start_download()
            self.logger.info(f"Auto-started download: {download_info['filename']}")

        # Update status
        self.update_status()
    
    def start_all_downloads(self):
        """Start all paused downloads"""
        for item in self.download_items.values():
            item.start_download()
    
    def pause_all_downloads(self):
        """Pause all active downloads"""
        for item in self.download_items.values():
            item.pause_download()
    
    def remove_completed(self):
        """Remove all completed downloads from list"""
        completed_items = []
        for download_id, item in self.download_items.items():
            if item.is_completed():
                completed_items.append(download_id)
        
        for download_id in completed_items:
            self.download_items[download_id].destroy()
            del self.download_items[download_id]
        
        # Show empty label if no downloads left
        if not self.download_items:
            self.empty_label.pack(expand=True)
        
        self.update_status()
    
    def download_youtube(self):
        """Show YouTube download dialog"""
        dialog = AddDownloadDialog(self.root, self.add_new_download, video_type="youtube")

    def download_tiktok(self):
        """Show TikTok download dialog"""
        dialog = AddDownloadDialog(self.root, self.add_new_download, video_type="tiktok")
    
    def show_settings(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self.root)
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{config.APP_NAME} v{config.APP_VERSION}

برنامج تحميل شامل يشبه IDM مع دعم تحميل الفيديوهات من:
• YouTube
• TikTok
• المواقع العامة

المطور: {config.APP_AUTHOR}
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def show_schedule_manager(self):
        """Show schedule manager dialog"""
        if self.scheduler:
            dialog = ScheduleManagerDialog(self.root, self.scheduler)
        else:
            messagebox.showwarning("تحذير", "مدير الجدولة غير متاح")

    def show_category_manager(self):
        """Show category manager dialog"""
        if self.category_manager:
            dialog = CategoryManagerDialog(self.root, self.category_manager)
        else:
            messagebox.showwarning("تحذير", "مدير التصنيفات غير متاح")

    def on_extension_download(self, download_info):
        """Handle download request from browser extension"""
        # Apply category manager if available
        if self.category_manager:
            category = self.category_manager.categorize_download(download_info)
            download_info['category'] = category

            # Update save path based on category
            base_path = download_info.get('save_path', config.DEFAULT_DOWNLOAD_PATH)
            category_path = self.category_manager.get_category_path(category, base_path)
            download_info['save_path'] = category_path

        # Check if we should skip schedule dialog for extension downloads
        if hasattr(config, 'SKIP_SCHEDULE_DIALOG') and config.SKIP_SCHEDULE_DIALOG:
            # Add download directly without showing schedule dialog
            self.add_new_download(download_info)
        else:
            # Show schedule dialog if scheduler is available
            if self.scheduler:
                dialog = ScheduleDialog(self.root, download_info, self.scheduler, self.on_download_added)
            else:
                # Add download directly
                self.add_new_download(download_info)
    
    def update_status(self):
        """Update status bar information"""
        active_downloads = sum(1 for item in self.download_items.values() if item.is_downloading())
        total_downloads = len(self.download_items)
        
        # Calculate total speed
        total_speed = sum(item.get_speed() for item in self.download_items.values())
        speed_text = self.format_speed(total_speed)
        
        self.info_label.configure(text=f"التحميلات: {active_downloads}/{total_downloads} | السرعة: {speed_text}")
    
    def format_speed(self, bytes_per_second):
        """Format speed in human readable format"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.1f} B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"
    
    def update_status_periodically(self):
        """Update status bar periodically"""
        self.update_status()
        # Schedule next update in 1 second
        self.root.after(1000, self.update_status_periodically)

    def on_closing(self):
        """Handle application closing"""
        if messagebox.askokcancel("خروج", "هل تريد إغلاق البرنامج؟"):
            try:
                # Stop all downloads first
                self.logger.info("Stopping all downloads...")
                for download_id, download_item in self.download_items.items():
                    try:
                        download_item.stop_download()
                    except Exception as e:
                        self.logger.error(f"Error stopping download {download_id}: {e}")

                # Stop download manager with timeout
                self.logger.info("Stopping download manager...")
                import threading
                import time

                def stop_download_manager():
                    try:
                        self.download_manager.stop()
                    except Exception as e:
                        self.logger.error(f"Error stopping download manager: {e}")

                # Run stop in separate thread with timeout
                stop_thread = threading.Thread(target=stop_download_manager, daemon=True)
                stop_thread.start()
                stop_thread.join(timeout=3)  # Wait max 3 seconds

                if stop_thread.is_alive():
                    self.logger.warning("Download manager stop timed out, forcing shutdown")

                # Stop scheduler if available
                if hasattr(self, 'scheduler') and self.scheduler:
                    self.logger.info("Stopping scheduler...")
                    try:
                        self.scheduler.stop()
                    except Exception as e:
                        self.logger.error(f"Error stopping scheduler: {e}")

                # Stop extension server if available
                if hasattr(self, 'extension_server') and self.extension_server:
                    self.logger.info("Stopping extension server...")
                    try:
                        self.extension_server.stop()
                    except Exception as e:
                        self.logger.error(f"Error stopping extension server: {e}")

                # Give remaining threads minimal time to stop
                time.sleep(0.5)

                self.logger.info("Application closing...")

            except Exception as e:
                self.logger.error(f"Error during shutdown: {e}")
            finally:
                # Force close application
                self.root.quit()
                self.root.destroy()

                # Force exit if needed
                import sys
                import os
                try:
                    os._exit(0)
                except:
                    sys.exit(0)
