"""
Browser Extension Communication Server
Handles communication between browser extension and download manager
"""

import threading
import json
import sys
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS
import uuid

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger
from src.utils.url_validator import URLValidator
from src.utils.file_utils import get_filename_from_url

class ExtensionServer:
    def __init__(self):
        self.app = Flask(__name__)
        CORS(self.app)  # Enable CORS for browser extension
        
        self.logger = get_logger(__name__)
        self.server_thread = None
        self.running = False
        self.download_callback = None
        
        self.setup_routes()
    
    def setup_routes(self):
        """Setup Flask routes for extension communication"""
        
        @self.app.route('/ping', methods=['GET'])
        def ping():
            """Health check endpoint"""
            return jsonify({'status': 'ok', 'message': 'Download manager is running'})
        
        @self.app.route('/download', methods=['POST'])
        def add_download():
            """Add new download from browser extension"""
            try:
                data = request.get_json()
                
                if not data or 'url' not in data:
                    return jsonify({'error': 'URL is required'}), 400
                
                url = data['url']
                
                # Validate URL
                if not URLValidator.is_valid_url(url):
                    return jsonify({'error': 'Invalid URL'}), 400
                
                # Get additional info from request
                filename = data.get('filename') or get_filename_from_url(url)
                save_path = data.get('save_path') or config.DEFAULT_DOWNLOAD_PATH
                quality = data.get('quality', 'best')
                
                # Determine video platform
                video_type = None
                if URLValidator.is_youtube_url(url):
                    video_type = 'youtube'
                elif URLValidator.is_tiktok_url(url):
                    video_type = 'tiktok'
                
                # Create download info
                download_info = {
                    'id': str(uuid.uuid4()),
                    'url': url,
                    'filename': filename,
                    'save_path': save_path,
                    'category': data.get('category', 'Others'),
                    'video_type': video_type,
                    'quality': quality,
                    'status': 'pending',
                    'progress': 0,
                    'speed': 0,
                    'size': 0,
                    'downloaded': 0,
                    'source': 'extension'
                }
                
                # Call download callback if set
                if self.download_callback:
                    self.download_callback(download_info)
                
                return jsonify({
                    'success': True,
                    'download_id': download_info['id'],
                    'message': 'Download added successfully'
                })
                
            except Exception as e:
                self.logger.error(f"Error adding download from extension: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/analyze', methods=['POST'])
        def analyze_url():
            """Analyze URL and return information"""
            try:
                data = request.get_json()
                
                if not data or 'url' not in data:
                    return jsonify({'error': 'URL is required'}), 400
                
                url = data['url']
                
                # Get URL info
                url_info = URLValidator.get_url_info(url)
                
                # Add video-specific info
                if url_info['platform'] == 'youtube':
                    from src.downloaders.youtube_downloader import YouTubeDownloader
                    yt_downloader = YouTubeDownloader()
                    video_info = yt_downloader.get_video_info(url)
                    
                    if video_info:
                        url_info.update({
                            'title': video_info.get('title'),
                            'duration': video_info.get('duration'),
                            'uploader': video_info.get('uploader'),
                            'thumbnail': video_info.get('thumbnail'),
                            'available_qualities': yt_downloader.get_available_qualities(url)
                        })
                
                elif url_info['platform'] == 'tiktok':
                    from src.downloaders.tiktok_downloader import TikTokDownloader
                    tt_downloader = TikTokDownloader()
                    video_info = tt_downloader.get_video_info(url)
                    
                    if video_info:
                        url_info.update({
                            'title': video_info.get('title'),
                            'duration': video_info.get('duration'),
                            'uploader': video_info.get('uploader'),
                            'thumbnail': video_info.get('thumbnail'),
                            'available_qualities': tt_downloader.get_available_qualities(url)
                        })
                
                return jsonify(url_info)
                
            except Exception as e:
                self.logger.error(f"Error analyzing URL: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/settings', methods=['GET'])
        def get_settings():
            """Get download manager settings"""
            try:
                import json
                settings_file = config.CONFIG_DIR / "settings.json"
                
                if settings_file.exists():
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                else:
                    settings = {
                        'download_path': config.DEFAULT_DOWNLOAD_PATH,
                        'auto_start': True
                    }
                
                return jsonify(settings)
                
            except Exception as e:
                self.logger.error(f"Error getting settings: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/downloads', methods=['GET'])
        def get_downloads():
            """Get current downloads status"""
            try:
                # This would need to be connected to the actual download manager
                # For now, return empty list
                return jsonify({'downloads': []})
                
            except Exception as e:
                self.logger.error(f"Error getting downloads: {e}")
                return jsonify({'error': str(e)}), 500
    
    def set_download_callback(self, callback):
        """Set callback function for new downloads"""
        self.download_callback = callback
    
    def start(self):
        """Start the extension server"""
        if self.running:
            return
        
        try:
            self.running = True
            
            # Start Flask server in a separate thread
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()
            
            self.logger.info(f"Extension server started on {config.EXTENSION_HOST}:{config.EXTENSION_PORT}")
            
        except Exception as e:
            self.logger.error(f"Error starting extension server: {e}")
            self.running = False
    
    def _run_server(self):
        """Run Flask server"""
        try:
            self.app.run(
                host=config.EXTENSION_HOST,
                port=config.EXTENSION_PORT,
                debug=False,
                use_reloader=False,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Extension server error: {e}")
            self.running = False
    
    def stop(self):
        """Stop the extension server"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # Note: Flask doesn't have a clean way to stop the server
            # In a production environment, you might want to use a different WSGI server
            
            self.logger.info("Extension server stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping extension server: {e}")
    
    def is_running(self):
        """Check if server is running"""
        return self.running
