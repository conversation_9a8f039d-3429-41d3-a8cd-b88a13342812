"""
Browser Extension Communication Server
Handles communication between browser extension and download manager
"""

import threading
import json
import sys
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS
import uuid

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger
from src.utils.url_validator import URLValidator
from src.utils.file_utils import get_filename_from_url

class ExtensionServer:
    def __init__(self):
        self.app = Flask(__name__)
        CORS(self.app)  # Enable CORS for browser extension

        self.logger = get_logger(__name__)
        self.server_thread = None
        self.running = False
        self.download_callback = None

        # Cache for video formats (URL -> {formats, timestamp})
        self.formats_cache = {}
        self.cache_timeout = 300  # 5 minutes

        self.setup_routes()

    def _is_cache_valid(self, url):
        """Check if cached formats are still valid"""
        if url not in self.formats_cache:
            return False

        import time
        cache_entry = self.formats_cache[url]
        return (time.time() - cache_entry['timestamp']) < self.cache_timeout

    def _get_cached_formats(self, url):
        """Get cached formats if available and valid"""
        if self._is_cache_valid(url):
            return self.formats_cache[url]['formats']
        return None

    def _cache_formats(self, url, formats):
        """Cache formats for a URL"""
        import time
        self.formats_cache[url] = {
            'formats': formats,
            'timestamp': time.time()
        }

    def setup_routes(self):
        """Setup Flask routes for extension communication"""
        
        @self.app.route('/ping', methods=['GET'])
        def ping():
            """Health check endpoint"""
            return jsonify({'status': 'ok', 'message': 'Download manager is running'})
        
        @self.app.route('/download', methods=['POST'])
        def add_download():
            """Add new download from browser extension"""
            try:
                data = request.get_json()
                
                if not data or 'url' not in data:
                    return jsonify({'error': 'URL is required'}), 400
                
                url = data['url']
                
                # Validate URL
                if not URLValidator.is_valid_url(url):
                    return jsonify({'error': 'Invalid URL'}), 400
                
                # Get additional info from request
                filename = data.get('filename') or get_filename_from_url(url)
                save_path = data.get('save_path') or config.DEFAULT_DOWNLOAD_PATH
                quality = data.get('quality', 'best')
                
                # Determine video platform
                video_type = None
                if URLValidator.is_youtube_url(url):
                    video_type = 'youtube'
                elif URLValidator.is_tiktok_url(url):
                    video_type = 'tiktok'
                
                # Create download info
                download_info = {
                    'id': str(uuid.uuid4()),
                    'url': url,
                    'filename': filename,
                    'save_path': save_path,
                    'category': data.get('category', 'Others'),
                    'video_type': video_type,
                    'quality': quality,
                    'status': 'pending',
                    'progress': 0,
                    'speed': 0,
                    'size': 0,
                    'downloaded': 0,
                    'source': 'extension'
                }

                # Add format information if provided
                if 'format' in data and data['format']:
                    download_info['format'] = data['format']
                    download_info['quality'] = data['format'].get('quality', quality)
                
                # Call download callback if set
                if self.download_callback:
                    self.download_callback(download_info)
                
                return jsonify({
                    'success': True,
                    'download_id': download_info['id'],
                    'message': 'Download added successfully'
                })
                
            except Exception as e:
                self.logger.error(f"Error adding download from extension: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/formats', methods=['POST'])
        def get_video_formats():
            """Get available video formats"""
            try:
                data = request.get_json()

                if not data or 'url' not in data:
                    return jsonify({'error': 'URL is required'}), 400

                url = data['url']
                self.logger.info(f"Getting formats for URL: {url}")

                # Validate URL
                validator = URLValidator()
                if not validator.is_valid_url(url):
                    return jsonify({'error': 'Invalid URL'}), 400

                # Check cache first
                cached_formats = self._get_cached_formats(url)
                if cached_formats:
                    self.logger.info(f"Using cached formats for URL: {url}")
                    return jsonify({
                        'success': True,
                        'formats': cached_formats,
                        'url': url,
                        'cached': True
                    })

                # Get formats using yt-dlp
                self.logger.info(f"Fetching fresh formats for URL: {url}")
                formats = self._get_youtube_formats(url)

                # Cache the results
                self._cache_formats(url, formats)

                return jsonify({
                    'success': True,
                    'formats': formats,
                    'url': url,
                    'cached': False
                })

            except Exception as e:
                self.logger.error(f"Error getting video formats: {e}")
                return jsonify({'error': str(e)}), 500

        @self.app.route('/analyze', methods=['POST'])
        def analyze_url():
            """Analyze URL and return information"""
            try:
                data = request.get_json()
                
                if not data or 'url' not in data:
                    return jsonify({'error': 'URL is required'}), 400
                
                url = data['url']
                
                # Get URL info
                url_info = URLValidator.get_url_info(url)
                
                # Add video-specific info
                if url_info['platform'] == 'youtube':
                    from src.downloaders.youtube_downloader import YouTubeDownloader
                    yt_downloader = YouTubeDownloader()
                    video_info = yt_downloader.get_video_info(url)
                    
                    if video_info:
                        url_info.update({
                            'title': video_info.get('title'),
                            'duration': video_info.get('duration'),
                            'uploader': video_info.get('uploader'),
                            'thumbnail': video_info.get('thumbnail'),
                            'available_qualities': yt_downloader.get_available_qualities(url)
                        })
                
                elif url_info['platform'] == 'tiktok':
                    from src.downloaders.tiktok_downloader import TikTokDownloader
                    tt_downloader = TikTokDownloader()
                    video_info = tt_downloader.get_video_info(url)
                    
                    if video_info:
                        url_info.update({
                            'title': video_info.get('title'),
                            'duration': video_info.get('duration'),
                            'uploader': video_info.get('uploader'),
                            'thumbnail': video_info.get('thumbnail'),
                            'available_qualities': tt_downloader.get_available_qualities(url)
                        })
                
                return jsonify(url_info)
                
            except Exception as e:
                self.logger.error(f"Error analyzing URL: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/settings', methods=['GET'])
        def get_settings():
            """Get download manager settings"""
            try:
                import json
                settings_file = config.CONFIG_DIR / "settings.json"
                
                if settings_file.exists():
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                else:
                    settings = {
                        'download_path': config.DEFAULT_DOWNLOAD_PATH,
                        'auto_start': True
                    }
                
                return jsonify(settings)
                
            except Exception as e:
                self.logger.error(f"Error getting settings: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/downloads', methods=['GET'])
        def get_downloads():
            """Get current downloads status"""
            try:
                # This would need to be connected to the actual download manager
                # For now, return empty list
                return jsonify({'downloads': []})
                
            except Exception as e:
                self.logger.error(f"Error getting downloads: {e}")
                return jsonify({'error': str(e)}), 500
    
    def set_download_callback(self, callback):
        """Set callback function for new downloads"""
        self.download_callback = callback
    
    def start(self):
        """Start the extension server"""
        if self.running:
            return
        
        try:
            self.running = True
            
            # Start Flask server in a separate thread
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()
            
            self.logger.info(f"Extension server started on {config.EXTENSION_HOST}:{config.EXTENSION_PORT}")
            
        except Exception as e:
            self.logger.error(f"Error starting extension server: {e}")
            self.running = False
    
    def _run_server(self):
        """Run Flask server"""
        try:
            self.app.run(
                host=config.EXTENSION_HOST,
                port=config.EXTENSION_PORT,
                debug=False,
                use_reloader=False,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Extension server error: {e}")
            self.running = False
    
    def stop(self):
        """Stop the extension server"""
        if not self.running:
            return
        
        try:
            self.running = False
            
            # Note: Flask doesn't have a clean way to stop the server
            # In a production environment, you might want to use a different WSGI server
            
            self.logger.info("Extension server stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping extension server: {e}")
    
    def is_running(self):
        """Check if server is running"""
        return self.running

    def _get_youtube_formats(self, url):
        """Get available formats for YouTube video using yt-dlp"""
        try:
            import yt_dlp

            # Configure yt-dlp options for fast format extraction
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
                'listformats': False,  # Don't list formats, just extract
                'skip_download': True,
                'no_check_certificate': True,
                'ignoreerrors': True,
                # Speed optimizations
                'writeinfojson': False,
                'writethumbnail': False,
                'writesubtitles': False,
                'writeautomaticsub': False,
                'socket_timeout': 10,  # 10 second timeout
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Extract info without downloading
                info = ydl.extract_info(url, download=False)

                if 'formats' not in info:
                    return []

                # Process and filter formats (show all useful formats)
                formats = []
                seen_formats = set()
                video_formats = []
                audio_formats = []

                for fmt in info['formats']:
                    format_id = fmt.get('format_id', '')

                    # Quick skip checks
                    if (not format_id or
                        format_id in seen_formats or
                        format_id.startswith('sb') or  # storyboard
                        format_id.startswith('hls')):  # HLS streams
                        continue

                    height = fmt.get('height') or 0
                    acodec = fmt.get('acodec', 'none')
                    vcodec = fmt.get('vcodec', 'none')

                    # Skip formats without proper codec info
                    if acodec == 'none' and vcodec == 'none':
                        continue

                    # Determine format type and playability
                    has_video = vcodec != 'none' and height > 0
                    has_audio = acodec != 'none'

                    # Determine format type
                    if has_video and has_audio:
                        format_type = 'complete'  # Complete format (video + audio)
                        playable = True
                    elif has_video and not has_audio:
                        format_type = 'video_only'  # Video only (needs audio merge)
                        playable = False
                    elif not has_video and has_audio:
                        format_type = 'audio_only'  # Audio only
                        playable = True
                    else:
                        format_type = 'unknown'
                        playable = False

                    # Create format info with detailed information
                    format_info = {
                        'format_id': format_id,
                        'ext': fmt.get('ext', 'mp4'),
                        'quality': self._get_quality_label_fast(fmt),
                        'filesize': fmt.get('filesize'),
                        'fps': fmt.get('fps'),
                        'width': fmt.get('width') or 0,
                        'height': height,
                        'tbr': fmt.get('tbr') or 0,
                        'vcodec': vcodec,
                        'acodec': acodec,
                        'format_type': format_type,
                        'playable': playable,
                        'needs_merge': format_type == 'video_only'
                    }

                    seen_formats.add(format_id)

                    # Categorize formats by type
                    if format_info['format_type'] == 'complete':
                        video_formats.append(format_info)
                    elif format_info['format_type'] == 'video_only':
                        video_formats.append(format_info)
                    elif format_info['format_type'] == 'audio_only':
                        audio_formats.append(format_info)

                # Sort formats with priority for playable formats
                def format_sort_key(fmt):
                    # Priority: playable first, then by quality
                    playable_priority = 0 if fmt['playable'] else 1
                    quality_priority = -(fmt['height'] or 0)
                    bitrate_priority = -(fmt['tbr'] or 0)
                    return (playable_priority, quality_priority, bitrate_priority)

                # Sort video formats (complete formats first, then video-only)
                video_formats.sort(key=format_sort_key)

                # Sort audio formats by bitrate (highest first)
                audio_formats.sort(key=lambda x: -(x['tbr'] or 0))

                # Combine formats: complete video first, then video-only, then audio
                complete_formats = [f for f in video_formats if f['playable']]
                video_only_formats = [f for f in video_formats if not f['playable']]

                # Limit formats for better performance
                formats = complete_formats[:8] + video_only_formats[:5] + audio_formats[:3]

                # Quick sort by quality (best first)
                formats.sort(key=lambda x: (x['height'] or 0, x['tbr'] or 0), reverse=True)

                return formats

        except ImportError:
            self.logger.error("yt-dlp not installed")
            return []
        except Exception as e:
            self.logger.error(f"Error getting YouTube formats: {e}")
            return []

    def _get_quality_label(self, fmt):
        """Get quality label for format"""
        height = fmt.get('height')
        if height:
            if height >= 2160:
                return '4K (2160p)'
            elif height >= 1440:
                return '1440p'
            elif height >= 1080:
                return '1080p'
            elif height >= 720:
                return '720p'
            elif height >= 480:
                return '480p'
            elif height >= 360:
                return '360p'
            elif height >= 240:
                return '240p'
            else:
                return f'{height}p'

        # Fallback to format note or quality
        if fmt.get('format_note'):
            return fmt['format_note']
        elif fmt.get('quality'):
            return str(fmt['quality'])
        else:
            return 'Unknown'

    def _get_quality_label_fast(self, fmt):
        """Get quality label for format (optimized version)"""
        height = fmt.get('height')
        acodec = fmt.get('acodec', 'none')
        vcodec = fmt.get('vcodec', 'none')

        # Video format with height
        if height and height > 0:
            # Use simple mapping for speed
            if height >= 2160: return '4K (2160p)'
            elif height >= 1440: return '1440p'
            elif height >= 1080: return '1080p'
            elif height >= 720: return '720p'
            elif height >= 480: return '480p'
            elif height >= 360: return '360p'
            elif height >= 240: return '240p'
            elif height >= 144: return '144p'
            else: return f'{height}p'

        # Audio-only format
        if acodec != 'none' and vcodec == 'none':
            return 'صوت فقط'

        # Mixed format (video + audio)
        if acodec != 'none' and vcodec != 'none':
            return 'فيديو + صوت'

        # Fallback
        if fmt.get('format_note'):
            return fmt['format_note']

        return 'غير محدد'

    def _get_quality_sort_key(self, format_info):
        """Get sort key for quality ordering"""
        height = format_info.get('height') or 0
        tbr = format_info.get('tbr') or 0

        # Convert to int if they're not None
        try:
            height = int(height) if height is not None else 0
            tbr = int(tbr) if tbr is not None else 0
        except (ValueError, TypeError):
            height = 0
            tbr = 0

        # Prefer higher resolution, then higher bitrate
        return (height, tbr)
