#!/usr/bin/env python3
"""
اختبار تحسينات السرعة لعرض الصيغ
"""

import time
import requests
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# URLs للاختبار
TEST_URLS = [
    "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll
    "https://www.youtube.com/watch?v=9bZkp7q19f0",  # Gangnam Style
    "https://www.youtube.com/watch?v=kJQP7kiw5Fk",  # Despacito
    "https://www.youtube.com/watch?v=fJ9rUzIMcZQ",  # Bohemian Rhapsody
    "https://www.youtube.com/watch?v=YQHsXMglC9A",  # Hello - Adele
]

SERVER_URL = "http://localhost:9876"

def test_format_speed(url, test_name=""):
    """اختبار سرعة الحصول على الصيغ"""
    print(f"\n🧪 اختبار {test_name}: {url}")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{SERVER_URL}/formats",
            json={"url": url},
            timeout=15
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                formats_count = len(data.get('formats', []))
                cached = data.get('cached', False)
                cache_status = "📦 مخزن مؤقتاً" if cached else "🆕 جديد"
                
                print(f"✅ نجح في {duration:.2f} ثانية")
                print(f"   📊 عدد الصيغ: {formats_count}")
                print(f"   {cache_status}")
                
                return {
                    'success': True,
                    'duration': duration,
                    'formats_count': formats_count,
                    'cached': cached,
                    'url': url
                }
            else:
                print(f"❌ فشل: {data.get('error', 'خطأ غير معروف')}")
                return {'success': False, 'error': data.get('error'), 'duration': duration}
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return {'success': False, 'error': f'HTTP {response.status_code}', 'duration': duration}
            
    except requests.exceptions.Timeout:
        duration = time.time() - start_time
        print(f"⏰ انتهت المهلة الزمنية ({duration:.2f} ثانية)")
        return {'success': False, 'error': 'Timeout', 'duration': duration}
        
    except Exception as e:
        duration = time.time() - start_time
        print(f"❌ خطأ: {str(e)}")
        return {'success': False, 'error': str(e), 'duration': duration}

def test_cache_effectiveness():
    """اختبار فعالية التخزين المؤقت"""
    print("\n🔄 اختبار فعالية التخزين المؤقت")
    print("=" * 50)
    
    test_url = TEST_URLS[0]
    
    # الطلب الأول (جديد)
    print("\n1️⃣ الطلب الأول (يجب أن يكون جديد):")
    result1 = test_format_speed(test_url, "طلب جديد")
    
    # الطلب الثاني (مخزن مؤقتاً)
    print("\n2️⃣ الطلب الثاني (يجب أن يكون مخزن مؤقتاً):")
    result2 = test_format_speed(test_url, "طلب مخزن")
    
    if result1['success'] and result2['success']:
        speed_improvement = ((result1['duration'] - result2['duration']) / result1['duration']) * 100
        print(f"\n📈 تحسن السرعة: {speed_improvement:.1f}%")
        print(f"   ⏱️ الطلب الأول: {result1['duration']:.2f} ثانية")
        print(f"   ⚡ الطلب الثاني: {result2['duration']:.2f} ثانية")
        
        if result2['cached']:
            print("✅ التخزين المؤقت يعمل بنجاح!")
        else:
            print("⚠️ التخزين المؤقت لا يعمل كما متوقع")
    
    return result1, result2

def test_concurrent_requests():
    """اختبار الطلبات المتزامنة"""
    print("\n🚀 اختبار الطلبات المتزامنة")
    print("=" * 50)
    
    start_time = time.time()
    results = []
    
    with ThreadPoolExecutor(max_workers=3) as executor:
        # إرسال طلبات متزامنة
        futures = {
            executor.submit(test_format_speed, url, f"متزامن {i+1}"): url 
            for i, url in enumerate(TEST_URLS[:3])
        }
        
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
    
    total_time = time.time() - start_time
    successful_requests = sum(1 for r in results if r['success'])
    
    print(f"\n📊 نتائج الطلبات المتزامنة:")
    print(f"   ⏱️ الوقت الإجمالي: {total_time:.2f} ثانية")
    print(f"   ✅ طلبات ناجحة: {successful_requests}/{len(results)}")
    
    if successful_requests > 0:
        avg_duration = sum(r['duration'] for r in results if r['success']) / successful_requests
        print(f"   📈 متوسط وقت الطلب: {avg_duration:.2f} ثانية")
    
    return results

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    print("🔌 اختبار الاتصال بالخادم...")
    
    try:
        response = requests.get(f"{SERVER_URL}/ping", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok':
                print("✅ الخادم متصل ويعمل")
                return True
            else:
                print(f"⚠️ الخادم يرد لكن بحالة غير طبيعية: {data}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار تحسينات السرعة لعرض الصيغ")
    print("=" * 60)
    
    # اختبار الاتصال أولاً
    if not test_server_connection():
        print("\n❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل البرنامج أولاً.")
        return
    
    # اختبار فعالية التخزين المؤقت
    cache_results = test_cache_effectiveness()
    
    # اختبار الطلبات المتزامنة
    concurrent_results = test_concurrent_requests()
    
    # اختبار سرعة مع URLs مختلفة
    print("\n🎯 اختبار سرعة مع فيديوهات مختلفة")
    print("=" * 50)
    
    individual_results = []
    for i, url in enumerate(TEST_URLS):
        result = test_format_speed(url, f"فيديو {i+1}")
        individual_results.append(result)
        time.sleep(1)  # فترة راحة بين الطلبات
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج")
    print("=" * 50)
    
    successful_tests = [r for r in individual_results if r['success']]
    if successful_tests:
        avg_time = sum(r['duration'] for r in successful_tests) / len(successful_tests)
        min_time = min(r['duration'] for r in successful_tests)
        max_time = max(r['duration'] for r in successful_tests)
        avg_formats = sum(r['formats_count'] for r in successful_tests) / len(successful_tests)
        
        print(f"✅ اختبارات ناجحة: {len(successful_tests)}/{len(individual_results)}")
        print(f"⏱️ متوسط الوقت: {avg_time:.2f} ثانية")
        print(f"🏃 أسرع طلب: {min_time:.2f} ثانية")
        print(f"🐌 أبطأ طلب: {max_time:.2f} ثانية")
        print(f"📊 متوسط عدد الصيغ: {avg_formats:.1f}")
        
        # تقييم الأداء
        if avg_time < 3:
            print("🎉 الأداء ممتاز! (أقل من 3 ثوان)")
        elif avg_time < 5:
            print("👍 الأداء جيد (أقل من 5 ثوان)")
        elif avg_time < 8:
            print("⚠️ الأداء مقبول (أقل من 8 ثوان)")
        else:
            print("🐌 الأداء بطيء (أكثر من 8 ثوان)")
    
    print("\n🎯 التوصيات:")
    print("- إذا كان الأداء بطيء، تحقق من اتصال الإنترنت")
    print("- التخزين المؤقت يحسن السرعة بشكل كبير للطلبات المتكررة")
    print("- الطلبات المتزامنة تعمل بكفاءة مع الخادم المحسن")

if __name__ == "__main__":
    main()
