"""
Installation script for Python Download Manager
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 أو أحدث مطلوب")
        print(f"الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """Install required packages"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 إنشاء المجلدات...")
    
    directories = [
        "downloads",
        "config", 
        "logs",
        "temp",
        "logs/downloads"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}")

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() != "Windows":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Python Download Manager.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = os.path.join(os.getcwd(), "icon.ico")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon if os.path.exists(icon) else ""
        shortcut.save()
        
        print("✅ تم إنشاء اختصار على سطح المكتب")
        
    except ImportError:
        print("⚠️ لإنشاء اختصار سطح المكتب، قم بتثبيت: pip install winshell pywin32")
    except Exception as e:
        print(f"⚠️ فشل في إنشاء اختصار سطح المكتب: {e}")

def setup_browser_extension():
    """Setup browser extension"""
    print("\n🌐 إعداد إضافة المتصفح:")
    print("1. افتح Chrome واذهب إلى chrome://extensions/")
    print("2. فعل 'وضع المطور' (Developer mode)")
    print("3. انقر على 'تحميل إضافة غير مُعبأة' (Load unpacked)")
    print(f"4. اختر مجلد: {os.path.join(os.getcwd(), 'browser_extension')}")
    print("\nللـ Firefox:")
    print("1. اذهب إلى about:debugging")
    print("2. انقر على 'This Firefox'")
    print("3. انقر على 'Load Temporary Add-on'")
    print("4. اختر ملف manifest.json من مجلد browser_extension")

def main():
    """Main installation function"""
    print("🚀 مرحباً بك في Python Download Manager")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    # Setup browser extension
    setup_browser_extension()
    
    print("\n" + "=" * 50)
    print("✅ تم التثبيت بنجاح!")
    print("\nلتشغيل البرنامج:")
    print("python main.py")
    print("\nأو استخدم الاختصار على سطح المكتب (Windows)")

if __name__ == "__main__":
    main()
