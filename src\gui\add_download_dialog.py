"""
Add Download Dialog for Python Download Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import os
import sys
from pathlib import Path
import uuid
import re

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.utils.url_validator import URLValidator
from src.utils.file_utils import get_filename_from_url, get_file_size

class AddDownloadDialog:
    def __init__(self, parent, callback, url="", video_type=None):
        self.parent = parent
        self.callback = callback
        self.video_type = video_type
        
        # Create dialog window
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("إضافة تحميل جديد")
        self.dialog.geometry("600x750")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.resizable(True, True)
        self.dialog.minsize(600, 750)
        
        # Center dialog
        self.center_dialog()
        
        # Initialize variables
        self.url_var = tk.StringVar(value=url)
        self.filename_var = tk.StringVar()
        self.save_path_var = tk.StringVar(value=config.DEFAULT_DOWNLOAD_PATH)
        self.quality_var = tk.StringVar()
        self.category_var = tk.StringVar(value="Others")
        
        self.create_widgets()
        
        # Focus on URL entry if empty
        if not url:
            self.url_entry.focus()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main scrollable frame
        main_frame = ctk.CTkScrollableFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Header
        ctk.CTkLabel(
            main_frame,
            text="إضافة تحميل جديد",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=(10, 20))
        
        # URL section
        url_frame = ctk.CTkFrame(main_frame)
        url_frame.pack(fill="x", pady=(0, 15))
        
        ctk.CTkLabel(url_frame, text="رابط التحميل:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))

        # Instructions
        instructions = ctk.CTkLabel(
            url_frame,
            text="يمكنك إضافة روابط من YouTube, TikTok, أو أي رابط مباشر للملف",
            font=ctk.CTkFont(size=11),
            text_color="gray"
        )
        instructions.pack(anchor="w", padx=15, pady=(0, 10))
        
        url_input_frame = ctk.CTkFrame(url_frame)
        url_input_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        self.url_entry = ctk.CTkEntry(
            url_input_frame,
            textvariable=self.url_var,
            placeholder_text="أدخل رابط التحميل هنا (YouTube, TikTok, أو رابط مباشر)...",
            height=40,
            font=ctk.CTkFont(size=12)
        )
        self.url_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        self.analyze_btn = ctk.CTkButton(
            url_input_frame,
            text="تحليل",
            command=self.analyze_url,
            width=80
        )
        self.analyze_btn.pack(side="right")
        
        # File info section
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 15))
        
        ctk.CTkLabel(info_frame, text="معلومات الملف:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        # Filename
        filename_frame = ctk.CTkFrame(info_frame)
        filename_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        ctk.CTkLabel(filename_frame, text="اسم الملف:").pack(anchor="w", padx=10, pady=(10, 5))
        self.filename_entry = ctk.CTkEntry(
            filename_frame,
            textvariable=self.filename_var,
            placeholder_text="سيتم تحديده تلقائياً..."
        )
        self.filename_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Save path
        path_frame = ctk.CTkFrame(info_frame)
        path_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        ctk.CTkLabel(path_frame, text="مجلد الحفظ:").pack(anchor="w", padx=10, pady=(10, 5))
        
        path_input_frame = ctk.CTkFrame(path_frame)
        path_input_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.path_entry = ctk.CTkEntry(
            path_input_frame,
            textvariable=self.save_path_var
        )
        self.path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        self.browse_btn = ctk.CTkButton(
            path_input_frame,
            text="تصفح",
            command=self.browse_folder,
            width=80
        )
        self.browse_btn.pack(side="right")
        
        # Category
        category_frame = ctk.CTkFrame(info_frame)
        category_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(category_frame, text="التصنيف:").pack(anchor="w", padx=10, pady=(10, 5))
        self.category_combo = ctk.CTkComboBox(
            category_frame,
            variable=self.category_var,
            values=list(config.FILE_CATEGORIES.keys()),
            state="readonly"
        )
        self.category_combo.pack(fill="x", padx=10, pady=(0, 10))
        
        # Quality section (for videos)
        self.quality_frame = ctk.CTkFrame(main_frame)
        
        if self.video_type:
            self.quality_frame.pack(fill="x", pady=(0, 15))
            
            ctk.CTkLabel(self.quality_frame, text="جودة الفيديو:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
            
            if self.video_type == "youtube":
                quality_options = config.YOUTUBE_QUALITY_OPTIONS
            elif self.video_type == "tiktok":
                quality_options = config.TIKTOK_QUALITY_OPTIONS
            else:
                quality_options = ["best", "worst"]
            
            self.quality_combo = ctk.CTkComboBox(
                self.quality_frame,
                variable=self.quality_var,
                values=quality_options,
                state="readonly"
            )
            self.quality_combo.set(quality_options[0])
            self.quality_combo.pack(fill="x", padx=15, pady=(0, 15))
        
        # Spacer
        ctk.CTkLabel(main_frame, text="", height=20).pack()

        # Simple buttons at bottom
        # Download button - VERY VISIBLE
        self.download_btn = ctk.CTkButton(
            main_frame,
            text="🔽 إضافة للتحميل",
            command=self.start_download,
            width=400,
            height=70,
            font=ctk.CTkFont(size=22, weight="bold"),
            fg_color=("#FF4444", "#CC3333"),
            hover_color=("#CC3333", "#FF4444")
        )
        self.download_btn.pack(pady=20)

        # Cancel button
        self.cancel_btn = ctk.CTkButton(
            main_frame,
            text="إلغاء",
            command=self.cancel,
            width=200,
            height=50,
            font=ctk.CTkFont(size=16),
            fg_color=("#888888", "#666666"),
            hover_color=("#666666", "#888888")
        )
        self.cancel_btn.pack(pady=(0, 20))
        
        # Bind URL entry change event
        self.url_var.trace_add('write', self.on_url_change)
        
        # If URL provided, analyze it
        if self.url_var.get():
            self.analyze_url()
    
    def on_url_change(self, var_name, index, operation):
        """Handle URL entry change"""
        url = self.url_var.get().strip()
        if url:
            # Auto-detect video type
            if 'youtube.com' in url or 'youtu.be' in url:
                self.video_type = "youtube"
                self.show_quality_options(config.YOUTUBE_QUALITY_OPTIONS)
            elif 'tiktok.com' in url:
                self.video_type = "tiktok"
                self.show_quality_options(config.TIKTOK_QUALITY_OPTIONS)
            else:
                self.video_type = None
                self.hide_quality_options()
    
    def show_quality_options(self, options):
        """Show quality selection for videos"""
        if not self.quality_frame.winfo_viewable():
            self.quality_frame.pack(fill="x", pady=(0, 15), before=self.quality_frame.master.winfo_children()[-1])
        
        if hasattr(self, 'quality_combo'):
            self.quality_combo.configure(values=options)
            self.quality_combo.set(options[0])
        else:
            ctk.CTkLabel(self.quality_frame, text="جودة الفيديو:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
            
            self.quality_combo = ctk.CTkComboBox(
                self.quality_frame,
                variable=self.quality_var,
                values=options,
                state="readonly"
            )
            self.quality_combo.set(options[0])
            self.quality_combo.pack(fill="x", padx=15, pady=(0, 15))
    
    def hide_quality_options(self):
        """Hide quality selection"""
        if self.quality_frame.winfo_viewable():
            self.quality_frame.pack_forget()
    
    def analyze_url(self):
        """Analyze URL and extract file information"""
        url = self.url_var.get().strip()
        
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط صالح")
            return
        
        if not URLValidator.is_valid_url(url):
            messagebox.showwarning("تحذير", "الرابط المدخل غير صالح")
            return
        
        try:
            # Show loading state
            self.analyze_btn.configure(text="جاري التحليل...")
            self.analyze_btn.configure(state="disabled")
            
            # Get filename from URL
            filename = get_filename_from_url(url)
            if filename:
                self.filename_var.set(filename)
                
                # Auto-detect category
                file_ext = Path(filename).suffix.lower()
                for category, extensions in config.FILE_CATEGORIES.items():
                    if file_ext in extensions:
                        self.category_var.set(category)
                        break
            
            # Reset button state
            self.analyze_btn.configure(text="تحليل")
            self.analyze_btn.configure(state="normal")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحليل الرابط: {str(e)}")
            self.analyze_btn.configure(text="تحليل")
            self.analyze_btn.configure(state="normal")
    
    def browse_folder(self):
        """Browse for save folder"""
        folder = filedialog.askdirectory(
            title="اختر مجلد الحفظ",
            initialdir=self.save_path_var.get()
        )
        if folder:
            self.save_path_var.set(folder)
    
    def start_download(self):
        """Start the download"""
        url = self.url_var.get().strip()
        filename = self.filename_var.get().strip()
        save_path = self.save_path_var.get().strip()
        
        # Validation
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال رابط التحميل")
            return
        
        if not URLValidator.is_valid_url(url):
            messagebox.showwarning("تحذير", "الرابط المدخل غير صالح")
            return
        
        if not filename:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الملف")
            return
        
        if not save_path or not os.path.exists(save_path):
            messagebox.showwarning("تحذير", "يرجى اختيار مجلد حفظ صالح")
            return
        
        # Create download info
        download_info = {
            'id': str(uuid.uuid4()),
            'url': url,
            'filename': filename,
            'save_path': save_path,
            'category': self.category_var.get(),
            'video_type': self.video_type,
            'quality': self.quality_var.get() if self.video_type else None,
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0
        }
        
        # Call callback with download info
        self.callback(download_info)
        
        # Close dialog
        self.dialog.destroy()
    
    def cancel(self):
        """Cancel and close dialog"""
        self.dialog.destroy()
