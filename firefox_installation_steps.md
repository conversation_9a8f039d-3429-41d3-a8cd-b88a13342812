# تثبيت الإضافة في Firefox

## الخطوة 1: فتح أدوات المطور
1. افتح Firefox
2. اكتب في شريط العنوان: `about:debugging`
3. اضغط Enter

## الخطوة 2: اختيار This Firefox
1. انقر على "This Firefox" في الجانب الأيسر
2. ستظهر صفحة إدارة الإضافات المؤقتة

## الخطوة 3: تحميل الإضافة
1. انقر على زر "Load Temporary Add-on..."
2. ستفتح نافذة اختيار الملفات
3. انتقل إلى مجلد: `browser_extensions/firefox/`
4. اختر ملف: `manifest.json`
5. انقر "Open"

## الخطوة 4: التحقق من التثبيت
1. ستظهر الإضافة في قائمة "Temporary Extensions"
2. ستجد: "🦊 Python Download Manager v1.0.0"
3. ستظهر أيقونة الإضافة في شريط الأدوات

## ملاحظات مهمة:
- ⚠️ الإضافة مؤقتة وستختفي عند إعادة تشغيل Firefox
- 🔄 لإعادة تحميلها: كرر الخطوات أو انقر "Reload"
- 🔍 للفحص: انقر "Inspect" لفتح أدوات المطور

## استكشاف الأخطاء:
- إذا لم تظهر الإضافة: تأكد من اختيار ملف manifest.json الصحيح
- إذا ظهر خطأ: تحقق من صحة ملفات الإضافة
- إذا لم تعمل: تأكد من تشغيل Python Download Manager على localhost:9876
