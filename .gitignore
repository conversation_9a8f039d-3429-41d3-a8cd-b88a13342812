# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
downloads/*
!downloads/.gitkeep
logs/*
!logs/.gitkeep
temp/*
!temp/.gitkeep
config/settings.json
config/downloads.db

# Browser extension (packed)
browser_extension/*.zip
browser_extension/*.crx

# Temporary files
*.tmp
*.temp
*.log

# Secrets
.env
secrets.json
