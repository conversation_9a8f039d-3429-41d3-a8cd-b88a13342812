"""
Setup script for Python Download Manager
"""

from setuptools import setup, find_packages
import os
from pathlib import Path

# Read README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
def read_requirements():
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="python-download-manager",
    version="1.0.0",
    author="Python DM Team",
    author_email="<EMAIL>",
    description="A comprehensive download manager similar to IDM with YouTube and TikTok support",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/python-download-manager",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP",
        "Topic :: Multimedia :: Video",
        "Topic :: System :: Archiving",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "build": [
            "pyinstaller>=5.0.0",
            "auto-py-to-exe>=2.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "python-dm=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.txt", "*.md", "*.yml", "*.yaml"],
        "browser_extension": ["*"],
    },
    zip_safe=False,
    keywords="download manager idm youtube tiktok video downloader",
    project_urls={
        "Bug Reports": "https://github.com/your-username/python-download-manager/issues",
        "Source": "https://github.com/your-username/python-download-manager",
        "Documentation": "https://github.com/your-username/python-download-manager/wiki",
    },
)
