"""
Duplicate File Dialog - Handle file conflicts when downloading
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from pathlib import Path
import sys
import os

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.file_utils import get_file_size, format_file_size, get_file_extension
import config

class DuplicateFileDialog:
    def __init__(self, parent, existing_file_path, new_filename, download_info=None):
        self.parent = parent
        self.existing_file_path = Path(existing_file_path)
        self.new_filename = new_filename
        self.download_info = download_info or {}
        self.result = None
        self.new_name = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create the duplicate file dialog"""
        self.dialog = ctk.CTkToplevel(self.parent)
        self.dialog.title("ملف موجود مسبقاً")
        self.dialog.geometry("600x500")
        self.dialog.resizable(<PERSON>als<PERSON>, False)
        
        # Make dialog modal
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Create content
        self.create_content()
        
        # Focus dialog
        self.dialog.focus()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate center position
        dialog_width = 600
        dialog_height = 500
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
    
    def create_content(self):
        """Create dialog content"""
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="⚠️ ملف موجود مسبقاً",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Message
        message_label = ctk.CTkLabel(
            main_frame,
            text="يوجد ملف بنفس الاسم في مجلد التحميل. ماذا تريد أن تفعل؟",
            font=ctk.CTkFont(size=14)
        )
        message_label.pack(pady=(0, 20))
        
        # File info frame
        self.create_file_info(main_frame)
        
        # Options frame
        self.create_options(main_frame)
        
        # Custom name frame (initially hidden)
        self.create_custom_name_frame(main_frame)
        
        # Buttons frame
        self.create_buttons(main_frame)
    
    def create_file_info(self, parent):
        """Create file information section"""
        info_frame = ctk.CTkFrame(parent)
        info_frame.pack(fill="x", padx=10, pady=10)
        
        # Existing file info
        existing_frame = ctk.CTkFrame(info_frame)
        existing_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(
            existing_frame,
            text="📁 الملف الموجود:",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(anchor="w", padx=10, pady=(10, 5))
        
        ctk.CTkLabel(
            existing_frame,
            text=f"الاسم: {self.existing_file_path.name}",
            font=ctk.CTkFont(size=12)
        ).pack(anchor="w", padx=20, pady=2)
        
        if self.existing_file_path.exists():
            existing_size = get_file_size(str(self.existing_file_path))
            ctk.CTkLabel(
                existing_frame,
                text=f"الحجم: {format_file_size(existing_size)}",
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", padx=20, pady=2)
            
            # File modification time
            import datetime
            mod_time = datetime.datetime.fromtimestamp(self.existing_file_path.stat().st_mtime)
            ctk.CTkLabel(
                existing_frame,
                text=f"تاريخ التعديل: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}",
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", padx=20, pady=(2, 10))
        
        # New file info
        new_frame = ctk.CTkFrame(info_frame)
        new_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(
            new_frame,
            text="📥 الملف الجديد:",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(anchor="w", padx=10, pady=(10, 5))
        
        ctk.CTkLabel(
            new_frame,
            text=f"الاسم: {self.new_filename}",
            font=ctk.CTkFont(size=12)
        ).pack(anchor="w", padx=20, pady=2)
        
        if 'size' in self.download_info and self.download_info['size'] > 0:
            ctk.CTkLabel(
                new_frame,
                text=f"الحجم: {format_file_size(self.download_info['size'])}",
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", padx=20, pady=(2, 10))
    
    def create_options(self, parent):
        """Create options section"""
        options_frame = ctk.CTkFrame(parent)
        options_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(
            options_frame,
            text="اختر العملية:",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Radio button variable
        self.option_var = tk.StringVar(value="rename")
        
        # Option 1: Rename automatically
        self.rename_radio = ctk.CTkRadioButton(
            options_frame,
            text="إعادة تسمية تلقائية (إضافة رقم)",
            variable=self.option_var,
            value="rename",
            command=self.on_option_change
        )
        self.rename_radio.pack(anchor="w", padx=20, pady=5)
        
        # Option 2: Replace existing file
        self.replace_radio = ctk.CTkRadioButton(
            options_frame,
            text="استبدال الملف الموجود",
            variable=self.option_var,
            value="replace",
            command=self.on_option_change
        )
        self.replace_radio.pack(anchor="w", padx=20, pady=5)
        
        # Option 3: Custom name
        self.custom_radio = ctk.CTkRadioButton(
            options_frame,
            text="اختيار اسم مخصص",
            variable=self.option_var,
            value="custom",
            command=self.on_option_change
        )
        self.custom_radio.pack(anchor="w", padx=20, pady=5)
        
        # Option 4: Skip download
        self.skip_radio = ctk.CTkRadioButton(
            options_frame,
            text="تخطي التحميل",
            variable=self.option_var,
            value="skip",
            command=self.on_option_change
        )
        self.skip_radio.pack(anchor="w", padx=20, pady=(5, 10))
    
    def create_custom_name_frame(self, parent):
        """Create custom name input frame"""
        self.custom_frame = ctk.CTkFrame(parent)
        # Initially hidden
        
        ctk.CTkLabel(
            self.custom_frame,
            text="اسم الملف الجديد:",
            font=ctk.CTkFont(size=12)
        ).pack(anchor="w", padx=10, pady=(10, 5))
        
        # Name input frame
        name_input_frame = ctk.CTkFrame(self.custom_frame)
        name_input_frame.pack(fill="x", padx=10, pady=5)
        
        self.custom_name_var = tk.StringVar(value=Path(self.new_filename).stem)
        self.custom_name_entry = ctk.CTkEntry(
            name_input_frame,
            textvariable=self.custom_name_var,
            font=ctk.CTkFont(size=12)
        )
        self.custom_name_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        # Extension label
        extension = get_file_extension(self.new_filename)
        self.extension_label = ctk.CTkLabel(
            name_input_frame,
            text=extension,
            font=ctk.CTkFont(size=12)
        )
        self.extension_label.pack(side="right", padx=(5, 10), pady=10)
        
        # Browse button
        self.browse_btn = ctk.CTkButton(
            self.custom_frame,
            text="تصفح...",
            command=self.browse_location,
            width=100
        )
        self.browse_btn.pack(anchor="w", padx=10, pady=(0, 10))
    
    def create_buttons(self, parent):
        """Create action buttons"""
        buttons_frame = ctk.CTkFrame(parent)
        buttons_frame.pack(fill="x", padx=10, pady=20)
        
        # Button container
        btn_container = ctk.CTkFrame(buttons_frame)
        btn_container.pack(pady=10)
        
        # OK button
        self.ok_btn = ctk.CTkButton(
            btn_container,
            text="موافق",
            command=self.on_ok,
            width=100
        )
        self.ok_btn.pack(side="left", padx=10)
        
        # Cancel button
        self.cancel_btn = ctk.CTkButton(
            btn_container,
            text="إلغاء",
            command=self.on_cancel,
            width=100
        )
        self.cancel_btn.pack(side="left", padx=10)
    
    def on_option_change(self):
        """Handle option change"""
        if self.option_var.get() == "custom":
            self.custom_frame.pack(fill="x", padx=10, pady=10)
            self.custom_name_entry.focus()
        else:
            self.custom_frame.pack_forget()
    
    def browse_location(self):
        """Browse for new file location"""
        initial_dir = self.existing_file_path.parent
        file_path = filedialog.asksaveasfilename(
            initialdir=initial_dir,
            initialfile=self.custom_name_var.get() + get_file_extension(self.new_filename),
            title="اختر موقع وأسم الملف الجديد"
        )
        
        if file_path:
            file_path = Path(file_path)
            self.custom_name_var.set(file_path.stem)
    
    def on_ok(self):
        """Handle OK button"""
        option = self.option_var.get()
        
        if option == "custom":
            custom_name = self.custom_name_var.get().strip()
            if not custom_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم للملف")
                return
            
            # Add extension
            extension = get_file_extension(self.new_filename)
            self.new_name = custom_name + extension
            
            # Check if new name conflicts
            new_path = self.existing_file_path.parent / self.new_name
            if new_path.exists() and new_path != self.existing_file_path:
                if not messagebox.askyesno("تأكيد", f"الملف '{self.new_name}' موجود أيضاً. هل تريد استبداله؟"):
                    return
        
        self.result = option
        self.dialog.destroy()
    
    def on_cancel(self):
        """Handle Cancel button"""
        self.result = "cancel"
        self.dialog.destroy()
    
    def show(self):
        """Show dialog and return result"""
        self.dialog.wait_window()
        return self.result, self.new_name

def show_duplicate_file_dialog(parent, existing_file_path, new_filename, download_info=None):
    """Show duplicate file dialog and return user choice"""
    dialog = DuplicateFileDialog(parent, existing_file_path, new_filename, download_info)
    return dialog.show()
