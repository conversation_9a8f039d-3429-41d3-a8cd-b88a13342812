#!/usr/bin/env python3
"""
Test duplicate file dialog
"""

import sys
import tkinter as tk
import customtkinter as ctk
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.gui.duplicate_file_dialog import show_duplicate_file_dialog

def test_duplicate_dialog():
    """Test the duplicate file dialog"""
    print("🧪 Testing Duplicate File Dialog...")
    
    # Create test window
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    root = ctk.CTk()
    root.title("Test Duplicate Dialog")
    root.geometry("400x300")
    
    # Create test file
    test_file = Path("downloads/test_video.mp4")
    test_file.parent.mkdir(exist_ok=True)
    test_file.touch()  # Create empty file
    
    # Test download info
    download_info = {
        'size': 50 * 1024 * 1024,  # 50 MB
        'url': 'https://example.com/video.mp4'
    }
    
    def show_dialog():
        result, new_name = show_duplicate_file_dialog(
            root,
            str(test_file),
            "test_video.mp4",
            download_info
        )
        
        print(f"Result: {result}")
        if new_name:
            print(f"New name: {new_name}")
        
        # Show result in main window
        result_label.configure(text=f"Result: {result}\nNew name: {new_name or 'None'}")
    
    # Create test button
    test_btn = ctk.CTkButton(
        root,
        text="Test Duplicate Dialog",
        command=show_dialog,
        font=ctk.CTkFont(size=16)
    )
    test_btn.pack(pady=50)
    
    # Result label
    result_label = ctk.CTkLabel(
        root,
        text="Click button to test dialog",
        font=ctk.CTkFont(size=14)
    )
    result_label.pack(pady=20)
    
    # Instructions
    instructions = ctk.CTkLabel(
        root,
        text="This will show a dialog for handling duplicate files.\nTry different options to test functionality.",
        font=ctk.CTkFont(size=12)
    )
    instructions.pack(pady=20)
    
    print("  📋 Test window created")
    print("  🖱️ Click 'Test Duplicate Dialog' button to test")
    
    root.mainloop()
    
    # Cleanup
    if test_file.exists():
        test_file.unlink()

if __name__ == "__main__":
    test_duplicate_dialog()
