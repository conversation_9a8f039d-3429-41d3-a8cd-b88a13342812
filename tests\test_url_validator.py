"""
Tests for URL Validator
"""

import pytest
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.url_validator import URLValidator

class TestURLValidator:
    """Test URL validation functionality"""
    
    def test_valid_urls(self):
        """Test valid URL detection"""
        valid_urls = [
            "https://www.example.com",
            "http://example.com",
            "https://youtube.com/watch?v=123",
            "https://www.tiktok.com/@user/video/123",
            "https://github.com/user/repo/releases/download/v1.0/file.zip"
        ]
        
        for url in valid_urls:
            assert URLValidator.is_valid_url(url), f"URL should be valid: {url}"
    
    def test_invalid_urls(self):
        """Test invalid URL detection"""
        invalid_urls = [
            "",
            "not-a-url",
            "ftp://example.com",  # Not HTTP/HTTPS
            "https://",
            "http://",
            None
        ]
        
        for url in invalid_urls:
            assert not URLValidator.is_valid_url(url), f"URL should be invalid: {url}"
    
    def test_youtube_url_detection(self):
        """Test YouTube URL detection"""
        youtube_urls = [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "https://youtube.com/watch?v=dQw4w9WgXcQ",
            "https://www.youtube.com/embed/dQw4w9WgXcQ"
        ]
        
        for url in youtube_urls:
            assert URLValidator.is_youtube_url(url), f"Should detect YouTube URL: {url}"
        
        non_youtube_urls = [
            "https://www.tiktok.com/@user/video/123",
            "https://www.example.com",
            "https://vimeo.com/123456"
        ]
        
        for url in non_youtube_urls:
            assert not URLValidator.is_youtube_url(url), f"Should not detect as YouTube URL: {url}"
    
    def test_tiktok_url_detection(self):
        """Test TikTok URL detection"""
        tiktok_urls = [
            "https://www.tiktok.com/@user/video/1234567890",
            "https://vm.tiktok.com/ZMeAbCdEf/",
            "https://tiktok.com/@user/video/1234567890"
        ]
        
        for url in tiktok_urls:
            assert URLValidator.is_tiktok_url(url), f"Should detect TikTok URL: {url}"
        
        non_tiktok_urls = [
            "https://www.youtube.com/watch?v=123",
            "https://www.example.com",
            "https://instagram.com/p/123"
        ]
        
        for url in non_tiktok_urls:
            assert not URLValidator.is_tiktok_url(url), f"Should not detect as TikTok URL: {url}"
    
    def test_video_platform_detection(self):
        """Test video platform detection"""
        test_cases = [
            ("https://www.youtube.com/watch?v=123", "youtube"),
            ("https://youtu.be/123", "youtube"),
            ("https://www.tiktok.com/@user/video/123", "tiktok"),
            ("https://vm.tiktok.com/123", "tiktok"),
            ("https://www.example.com", None),
        ]
        
        for url, expected_platform in test_cases:
            platform = URLValidator.get_video_platform(url)
            assert platform == expected_platform, f"URL {url} should return platform {expected_platform}, got {platform}"
    
    def test_url_normalization(self):
        """Test URL normalization"""
        test_cases = [
            ("example.com", "https://example.com"),
            ("www.example.com", "https://www.example.com"),
            ("https://example.com", "https://example.com"),
            ("http://example.com", "http://example.com"),
            ("", ""),
            (None, None)
        ]
        
        for input_url, expected_output in test_cases:
            normalized = URLValidator.normalize_url(input_url)
            assert normalized == expected_output, f"URL {input_url} should normalize to {expected_output}, got {normalized}"
    
    def test_domain_extraction(self):
        """Test domain extraction from URLs"""
        test_cases = [
            ("https://www.example.com/path", "www.example.com"),
            ("http://subdomain.example.com:8080/path", "subdomain.example.com:8080"),
            ("https://youtube.com/watch?v=123", "youtube.com"),
            ("invalid-url", None),
            ("", None)
        ]
        
        for url, expected_domain in test_cases:
            domain = URLValidator.get_domain(url)
            assert domain == expected_domain, f"URL {url} should return domain {expected_domain}, got {domain}"
    
    def test_secure_url_detection(self):
        """Test HTTPS URL detection"""
        secure_urls = [
            "https://www.example.com",
            "https://youtube.com/watch?v=123"
        ]
        
        insecure_urls = [
            "http://www.example.com",
            "ftp://example.com",
            "invalid-url"
        ]
        
        for url in secure_urls:
            assert URLValidator.is_secure_url(url), f"URL should be secure: {url}"
        
        for url in insecure_urls:
            assert not URLValidator.is_secure_url(url), f"URL should not be secure: {url}"

if __name__ == "__main__":
    pytest.main([__file__])
