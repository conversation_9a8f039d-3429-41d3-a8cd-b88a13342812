version: '3.8'

services:
  python-download-manager:
    build: .
    container_name: python-dm
    ports:
      - "9876:9876"
    volumes:
      - ./downloads:/app/downloads
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9876/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a web interface (future enhancement)
  # web-interface:
  #   build: ./web
  #   container_name: python-dm-web
  #   ports:
  #     - "8080:8080"
  #   depends_on:
  #     - python-download-manager
  #   restart: unless-stopped

volumes:
  downloads:
  config:
  logs:
