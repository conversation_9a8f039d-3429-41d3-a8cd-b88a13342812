"""
Database Manager for Python Download Manager
Handles download history and state persistence
"""

import sqlite3
import json
import threading
from datetime import datetime
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger

class DatabaseManager:
    def __init__(self):
        self.db_path = config.DATABASE_PATH
        self.logger = get_logger(__name__)
        self.lock = threading.Lock()
        
    def initialize_database(self):
        """Initialize database tables"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Create downloads table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS downloads (
                        id TEXT PRIMARY KEY,
                        url TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        save_path TEXT NOT NULL,
                        category TEXT,
                        video_type TEXT,
                        quality TEXT,
                        status TEXT DEFAULT 'pending',
                        progress REAL DEFAULT 0,
                        speed REAL DEFAULT 0,
                        size INTEGER DEFAULT 0,
                        downloaded INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_at TIMESTAMP,
                        error_message TEXT
                    )
                ''')
                
                # Create download_history table for completed downloads
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS download_history (
                        id TEXT PRIMARY KEY,
                        url TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        save_path TEXT NOT NULL,
                        category TEXT,
                        size INTEGER,
                        download_time REAL,
                        average_speed REAL,
                        completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS settings (
                        key TEXT PRIMARY KEY,
                        value TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                conn.close()
                
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            raise
    
    def add_download(self, download_info):
        """Add a new download to database"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO downloads 
                    (id, url, filename, save_path, category, video_type, quality, status, 
                     progress, speed, size, downloaded, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    download_info['id'],
                    download_info['url'],
                    download_info['filename'],
                    download_info['save_path'],
                    download_info.get('category'),
                    download_info.get('video_type'),
                    download_info.get('quality'),
                    download_info.get('status', 'pending'),
                    download_info.get('progress', 0),
                    download_info.get('speed', 0),
                    download_info.get('size', 0),
                    download_info.get('downloaded', 0),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                conn.close()
                
                self.logger.info(f"Added download to database: {download_info['filename']}")
                
        except Exception as e:
            self.logger.error(f"Error adding download to database: {e}")
    
    def update_download_progress(self, download_id, progress_info):
        """Update download progress in database"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE downloads 
                    SET progress = ?, speed = ?, downloaded = ?, size = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    progress_info.get('progress', 0),
                    progress_info.get('speed', 0),
                    progress_info.get('downloaded', 0),
                    progress_info.get('size', 0),
                    datetime.now().isoformat(),
                    download_id
                ))
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            self.logger.error(f"Error updating download progress: {e}")
    
    def update_download_status(self, download_id, status, error_message=None):
        """Update download status in database"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                completed_at = datetime.now().isoformat() if status == 'completed' else None
                
                cursor.execute('''
                    UPDATE downloads 
                    SET status = ?, updated_at = ?, completed_at = ?, error_message = ?
                    WHERE id = ?
                ''', (
                    status,
                    datetime.now().isoformat(),
                    completed_at,
                    error_message,
                    download_id
                ))
                
                conn.commit()
                conn.close()
                
                # If completed, move to history
                if status == 'completed':
                    self._move_to_history(download_id)
                
        except Exception as e:
            self.logger.error(f"Error updating download status: {e}")
    
    def _move_to_history(self, download_id):
        """Move completed download to history table"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Get download info
                cursor.execute('SELECT * FROM downloads WHERE id = ?', (download_id,))
                download = cursor.fetchone()
                
                if download:
                    # Calculate download time and average speed
                    created_at = datetime.fromisoformat(download[12])  # created_at
                    completed_at = datetime.fromisoformat(download[14])  # completed_at
                    download_time = (completed_at - created_at).total_seconds()
                    
                    size = download[10]  # size
                    average_speed = size / download_time if download_time > 0 else 0
                    
                    # Insert into history
                    cursor.execute('''
                        INSERT OR REPLACE INTO download_history
                        (id, url, filename, save_path, category, size, download_time, 
                         average_speed, completed_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        download[0],  # id
                        download[1],  # url
                        download[2],  # filename
                        download[3],  # save_path
                        download[4],  # category
                        size,
                        download_time,
                        average_speed,
                        download[14]  # completed_at
                    ))
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            self.logger.error(f"Error moving download to history: {e}")
    
    def get_download(self, download_id):
        """Get download information by ID"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM downloads WHERE id = ?', (download_id,))
                row = cursor.fetchone()
                
                conn.close()
                
                if row:
                    return self._row_to_dict(row)
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting download: {e}")
            return None
    
    def get_all_downloads(self):
        """Get all downloads"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM downloads ORDER BY created_at DESC')
                rows = cursor.fetchall()
                
                conn.close()
                
                return [self._row_to_dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Error getting all downloads: {e}")
            return []
    
    def get_incomplete_downloads(self):
        """Get all incomplete downloads"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM downloads 
                    WHERE status IN ('pending', 'downloading', 'paused', 'error')
                    ORDER BY created_at DESC
                ''')
                rows = cursor.fetchall()
                
                conn.close()
                
                return [self._row_to_dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Error getting incomplete downloads: {e}")
            return []
    
    def remove_download(self, download_id):
        """Remove download from database"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('DELETE FROM downloads WHERE id = ?', (download_id,))
                
                conn.commit()
                conn.close()
                
                self.logger.info(f"Removed download from database: {download_id}")
                
        except Exception as e:
            self.logger.error(f"Error removing download: {e}")
    
    def get_download_history(self, limit=100):
        """Get download history"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM download_history 
                    ORDER BY completed_at DESC 
                    LIMIT ?
                ''', (limit,))
                rows = cursor.fetchall()
                
                conn.close()
                
                return [self._history_row_to_dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Error getting download history: {e}")
            return []
    
    def get_statistics(self):
        """Get download statistics"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Total downloads
                cursor.execute('SELECT COUNT(*) FROM downloads')
                total_downloads = cursor.fetchone()[0]
                
                # Completed downloads
                cursor.execute('SELECT COUNT(*) FROM download_history')
                completed_downloads = cursor.fetchone()[0]
                
                # Total downloaded size
                cursor.execute('SELECT SUM(size) FROM download_history')
                total_size = cursor.fetchone()[0] or 0
                
                # Average speed
                cursor.execute('SELECT AVG(average_speed) FROM download_history')
                avg_speed = cursor.fetchone()[0] or 0
                
                conn.close()
                
                return {
                    'total_downloads': total_downloads,
                    'completed_downloads': completed_downloads,
                    'total_size': total_size,
                    'average_speed': avg_speed
                }
                
        except Exception as e:
            self.logger.error(f"Error getting statistics: {e}")
            return {}
    
    def _row_to_dict(self, row):
        """Convert database row to dictionary"""
        return {
            'id': row[0],
            'url': row[1],
            'filename': row[2],
            'save_path': row[3],
            'category': row[4],
            'video_type': row[5],
            'quality': row[6],
            'status': row[7],
            'progress': row[8],
            'speed': row[9],
            'size': row[10],
            'downloaded': row[11],
            'created_at': row[12],
            'updated_at': row[13],
            'completed_at': row[14],
            'error_message': row[15]
        }
    
    def _history_row_to_dict(self, row):
        """Convert history row to dictionary"""
        return {
            'id': row[0],
            'url': row[1],
            'filename': row[2],
            'save_path': row[3],
            'category': row[4],
            'size': row[5],
            'download_time': row[6],
            'average_speed': row[7],
            'completed_at': row[8]
        }
