/**
 * Firefox Background Script
 * Python Download Manager Extension
 */

// استخدام browser API مع fallback لـ chrome API
const browserAPI = typeof browser !== 'undefined' ? browser : chrome;

// إعدادات الخادم
const SERVER_URL = 'http://localhost:9876';

// تسجيل بدء الإضافة
console.log('🦊 Firefox Extension: Python Download Manager started');

/**
 * معالج الرسائل من content script
 */
browserAPI.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 Firefox: Received message:', request.action);
    
    switch (request.action) {
        case 'download':
            handleDownload(request.videoInfo)
                .then(response => sendResponse(response))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true; // للاستجابة غير المتزامنة
            
        case 'getFormats':
            getVideoFormats(request.videoInfo)
                .then(response => sendResponse(response))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        case 'checkServer':
            checkServerStatus()
                .then(response => sendResponse(response))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
            
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
});

/**
 * معالجة طلب التحميل
 */
async function handleDownload(videoInfo) {
    try {
        console.log('🎬 Firefox: Starting download for:', videoInfo.title);
        
        const response = await fetch(`${SERVER_URL}/download`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: videoInfo.url,
                title: videoInfo.title,
                format_info: videoInfo.format_info || null
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('✅ Firefox: Download response:', data);
        
        return {
            success: true,
            message: data.message || 'تم إضافة التحميل بنجاح',
            data: data
        };
        
    } catch (error) {
        console.error('❌ Firefox: Download error:', error);
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return {
                success: false,
                error: 'البرنامج غير متصل. تأكد من تشغيل Python Download Manager'
            };
        }
        
        return {
            success: false,
            error: error.message || 'خطأ في التحميل'
        };
    }
}

/**
 * الحصول على صيغ الفيديو المتاحة
 */
async function getVideoFormats(videoInfo) {
    try {
        console.log('📊 Firefox: Getting formats for:', videoInfo.url);
        
        const response = await fetch(`${SERVER_URL}/formats`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: videoInfo.url
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log(`✅ Firefox: Got ${data.formats?.length || 0} formats`);
        
        return {
            success: true,
            formats: data.formats || [],
            cached: data.cached || false
        };
        
    } catch (error) {
        console.error('❌ Firefox: Get formats error:', error);
        
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return {
                success: false,
                error: 'البرنامج غير متصل. تأكد من تشغيل Python Download Manager'
            };
        }
        
        return {
            success: false,
            error: error.message || 'خطأ في الحصول على الصيغ'
        };
    }
}

/**
 * فحص حالة الخادم
 */
async function checkServerStatus() {
    try {
        const response = await fetch(`${SERVER_URL}/ping`, {
            method: 'GET',
            timeout: 5000
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        return {
            success: true,
            status: data.status,
            message: data.message
        };
        
    } catch (error) {
        console.error('❌ Firefox: Server check error:', error);
        
        return {
            success: false,
            error: 'الخادم غير متاح'
        };
    }
}

/**
 * معالج تحديث التبويب
 */
browserAPI.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // التحقق من تحميل صفحة YouTube
    if (changeInfo.status === 'complete' && tab.url && 
        (tab.url.includes('youtube.com') || tab.url.includes('youtu.be'))) {
        
        console.log('🎬 Firefox: YouTube page loaded:', tab.url);
        
        // يمكن إضافة منطق إضافي هنا
    }
});

/**
 * معالج تثبيت الإضافة
 */
browserAPI.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('🎉 Firefox: Extension installed successfully');
        
        // فتح صفحة الترحيب (اختياري)
        // browserAPI.tabs.create({
        //     url: 'https://github.com/augment-code/python-download-manager'
        // });
    } else if (details.reason === 'update') {
        console.log('🔄 Firefox: Extension updated to version', browserAPI.runtime.getManifest().version);
    }
});

/**
 * معالج بدء تشغيل المتصفح
 */
browserAPI.runtime.onStartup.addListener(() => {
    console.log('🚀 Firefox: Browser started, extension ready');
});

// تصدير للاختبار
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        handleDownload,
        getVideoFormats,
        checkServerStatus
    };
}
