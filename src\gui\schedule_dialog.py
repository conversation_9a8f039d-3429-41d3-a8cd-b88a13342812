"""
Schedule Dialog for Python Download Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

class ScheduleDialog:
    def __init__(self, parent, download_info, scheduler, callback=None):
        self.parent = parent
        self.download_info = download_info
        self.scheduler = scheduler
        self.callback = callback
        
        # Create dialog window
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("جدولة التحميل")
        self.dialog.geometry("500x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Initialize variables
        self.schedule_type_var = tk.StringVar(value="now")
        self.date_var = tk.StringVar()
        self.time_var = tk.StringVar()
        self.repeat_var = tk.StringVar(value="none")
        
        # Set default date and time
        now = datetime.now()
        self.date_var.set(now.strftime("%Y-%m-%d"))
        self.time_var.set(now.strftime("%H:%M"))
        
        self.create_widgets()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Download info section
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(info_frame, text="معلومات التحميل:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        # Filename
        filename_frame = ctk.CTkFrame(info_frame)
        filename_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        ctk.CTkLabel(filename_frame, text="الملف:").pack(anchor="w", padx=10, pady=(10, 5))
        ctk.CTkLabel(filename_frame, text=self.download_info['filename'], 
                    wraplength=400, justify="right").pack(anchor="w", padx=10, pady=(0, 10))
        
        # URL
        url_frame = ctk.CTkFrame(info_frame)
        url_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(url_frame, text="الرابط:").pack(anchor="w", padx=10, pady=(10, 5))
        url_text = self.download_info['url']
        if len(url_text) > 60:
            url_text = url_text[:57] + "..."
        ctk.CTkLabel(url_frame, text=url_text, 
                    wraplength=400, justify="right").pack(anchor="w", padx=10, pady=(0, 10))
        
        # Schedule type section
        schedule_frame = ctk.CTkFrame(main_frame)
        schedule_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(schedule_frame, text="نوع الجدولة:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 10))
        
        # Schedule type options
        options_frame = ctk.CTkFrame(schedule_frame)
        options_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        self.now_radio = ctk.CTkRadioButton(
            options_frame,
            text="بدء التحميل الآن",
            variable=self.schedule_type_var,
            value="now",
            command=self.on_schedule_type_change
        )
        self.now_radio.pack(anchor="w", padx=10, pady=5)
        
        self.later_radio = ctk.CTkRadioButton(
            options_frame,
            text="جدولة لوقت لاحق",
            variable=self.schedule_type_var,
            value="later",
            command=self.on_schedule_type_change
        )
        self.later_radio.pack(anchor="w", padx=10, pady=5)
        
        self.queue_radio = ctk.CTkRadioButton(
            options_frame,
            text="إضافة إلى قائمة الانتظار",
            variable=self.schedule_type_var,
            value="queue",
            command=self.on_schedule_type_change
        )
        self.queue_radio.pack(anchor="w", padx=10, pady=5)
        
        # Date and time section
        self.datetime_frame = ctk.CTkFrame(main_frame)
        
        ctk.CTkLabel(self.datetime_frame, text="التاريخ والوقت:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 10))
        
        # Date selection
        date_frame = ctk.CTkFrame(self.datetime_frame)
        date_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        ctk.CTkLabel(date_frame, text="التاريخ:").pack(anchor="w", padx=10, pady=(10, 5))
        self.date_entry = ctk.CTkEntry(
            date_frame,
            textvariable=self.date_var,
            placeholder_text="YYYY-MM-DD"
        )
        self.date_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Time selection
        time_frame = ctk.CTkFrame(self.datetime_frame)
        time_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        ctk.CTkLabel(time_frame, text="الوقت:").pack(anchor="w", padx=10, pady=(10, 5))
        self.time_entry = ctk.CTkEntry(
            time_frame,
            textvariable=self.time_var,
            placeholder_text="HH:MM"
        )
        self.time_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Quick time buttons
        quick_time_frame = ctk.CTkFrame(self.datetime_frame)
        quick_time_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(quick_time_frame, text="أوقات سريعة:").pack(anchor="w", padx=10, pady=(10, 5))
        
        buttons_frame = ctk.CTkFrame(quick_time_frame)
        buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        quick_buttons = [
            ("خلال ساعة", 1),
            ("خلال 3 ساعات", 3),
            ("خلال 6 ساعات", 6),
            ("غداً", 24)
        ]
        
        for i, (text, hours) in enumerate(quick_buttons):
            btn = ctk.CTkButton(
                buttons_frame,
                text=text,
                command=lambda h=hours: self.set_quick_time(h),
                width=100,
                height=30
            )
            btn.grid(row=i//2, column=i%2, padx=5, pady=5, sticky="ew")
        
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)
        
        # Repeat section
        repeat_frame = ctk.CTkFrame(main_frame)
        repeat_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(repeat_frame, text="التكرار:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 10))
        
        self.repeat_combo = ctk.CTkComboBox(
            repeat_frame,
            variable=self.repeat_var,
            values=["لا يتكرر", "يومياً", "أسبوعياً", "شهرياً"],
            state="readonly"
        )
        self.repeat_combo.pack(fill="x", padx=15, pady=(0, 15))
        
        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x")
        
        self.cancel_btn = ctk.CTkButton(
            button_frame,
            text="إلغاء",
            command=self.cancel,
            width=100
        )
        self.cancel_btn.pack(side="right", padx=15, pady=15)
        
        self.schedule_btn = ctk.CTkButton(
            button_frame,
            text="جدولة",
            command=self.schedule_download,
            width=100
        )
        self.schedule_btn.pack(side="right", padx=(15, 10), pady=15)
        
        # Initial state
        self.on_schedule_type_change()
    
    def on_schedule_type_change(self):
        """Handle schedule type change"""
        schedule_type = self.schedule_type_var.get()
        
        if schedule_type == "later":
            self.datetime_frame.pack(fill="x", pady=(0, 20), before=self.datetime_frame.master.winfo_children()[-2])
        else:
            self.datetime_frame.pack_forget()
    
    def set_quick_time(self, hours):
        """Set quick time option"""
        target_time = datetime.now() + timedelta(hours=hours)
        self.date_var.set(target_time.strftime("%Y-%m-%d"))
        self.time_var.set(target_time.strftime("%H:%M"))
    
    def validate_datetime(self):
        """Validate date and time input"""
        try:
            date_str = self.date_var.get()
            time_str = self.time_var.get()
            
            # Parse date and time
            datetime_str = f"{date_str} {time_str}"
            scheduled_time = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
            
            # Check if time is in the future
            if scheduled_time <= datetime.now():
                messagebox.showwarning("تحذير", "يجب أن يكون الوقت المحدد في المستقبل")
                return None
            
            return scheduled_time
            
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ أو الوقت غير صحيح")
            return None
    
    def schedule_download(self):
        """Schedule the download"""
        try:
            schedule_type = self.schedule_type_var.get()
            
            if schedule_type == "now":
                # Start download immediately
                if self.callback:
                    self.callback(self.download_info)
                self.dialog.destroy()
                return
            
            elif schedule_type == "queue":
                # Add to queue (implement queue logic)
                if self.callback:
                    self.callback(self.download_info)
                messagebox.showinfo("نجح", "تم إضافة التحميل إلى قائمة الانتظار")
                self.dialog.destroy()
                return
            
            elif schedule_type == "later":
                # Schedule for later
                scheduled_time = self.validate_datetime()
                if not scheduled_time:
                    return
                
                # Get repeat type
                repeat_mapping = {
                    "لا يتكرر": "none",
                    "يومياً": "daily",
                    "أسبوعياً": "weekly",
                    "شهرياً": "monthly"
                }
                repeat_type = repeat_mapping.get(self.repeat_var.get(), "none")
                
                # Schedule the download
                schedule_id = self.scheduler.schedule_download(
                    self.download_info,
                    scheduled_time,
                    repeat_type
                )
                
                if schedule_id:
                    messagebox.showinfo("نجح", f"تم جدولة التحميل لـ {scheduled_time.strftime('%Y-%m-%d %H:%M')}")
                    self.dialog.destroy()
                else:
                    messagebox.showerror("خطأ", "فشل في جدولة التحميل")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
    
    def cancel(self):
        """Cancel and close dialog"""
        self.dialog.destroy()
