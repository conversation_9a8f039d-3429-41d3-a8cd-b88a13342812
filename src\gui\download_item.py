"""
Download Item Widget for Python Download Manager
Individual download item in the downloads list
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import threading
import time
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

class DownloadItem(ctk.CTkFrame):
    def __init__(self, parent, download_info, download_manager):
        super().__init__(parent)
        
        self.download_info = download_info
        self.download_manager = download_manager
        self.is_downloading_flag = False
        self.current_speed = 0
        
        self.create_widgets()
        self.update_display()
    
    def create_widgets(self):
        """Create download item widgets"""
        # Main container
        self.configure(height=80, corner_radius=8)
        self.grid_columnconfigure(1, weight=1)
        
        # File icon (placeholder)
        self.icon_label = ctk.CTkLabel(
            self,
            text="📁",
            font=ctk.CTkFont(size=24),
            width=40
        )
        self.icon_label.grid(row=0, column=0, rowspan=3, padx=10, pady=10, sticky="nsw")
        
        # File info frame
        info_frame = ctk.CTkFrame(self, fg_color="transparent")
        info_frame.grid(row=0, column=1, columnspan=2, sticky="ew", padx=(0, 10), pady=(10, 5))
        info_frame.grid_columnconfigure(0, weight=1)
        
        # Filename
        self.filename_label = ctk.CTkLabel(
            info_frame,
            text=self.download_info['filename'],
            font=ctk.CTkFont(weight="bold"),
            anchor="w"
        )
        self.filename_label.grid(row=0, column=0, sticky="ew")
        
        # URL (truncated)
        url_text = self.download_info['url']
        if len(url_text) > 60:
            url_text = url_text[:57] + "..."
        
        self.url_label = ctk.CTkLabel(
            info_frame,
            text=url_text,
            font=ctk.CTkFont(size=11),
            text_color="gray",
            anchor="w"
        )
        self.url_label.grid(row=1, column=0, sticky="ew")
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(self)
        self.progress_bar.grid(row=1, column=1, sticky="ew", padx=(0, 10), pady=5)
        self.progress_bar.set(0)
        
        # Status and controls frame
        controls_frame = ctk.CTkFrame(self, fg_color="transparent")
        controls_frame.grid(row=2, column=1, columnspan=2, sticky="ew", padx=(0, 10), pady=(0, 10))
        
        # Status info
        self.status_label = ctk.CTkLabel(
            controls_frame,
            text="في الانتظار",
            font=ctk.CTkFont(size=11),
            anchor="w"
        )
        self.status_label.pack(side="left")
        
        # Control buttons frame
        buttons_frame = ctk.CTkFrame(controls_frame, fg_color="transparent")
        buttons_frame.pack(side="right")
        
        # Start/Pause button
        self.start_pause_btn = ctk.CTkButton(
            buttons_frame,
            text="▶️",
            command=self.toggle_download,
            width=30,
            height=25,
            font=ctk.CTkFont(size=12)
        )
        self.start_pause_btn.pack(side="left", padx=2)
        
        # Stop button
        self.stop_btn = ctk.CTkButton(
            buttons_frame,
            text="⏹️",
            command=self.stop_download,
            width=30,
            height=25,
            font=ctk.CTkFont(size=12)
        )
        self.stop_btn.pack(side="left", padx=2)
        
        # Remove button
        self.remove_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️",
            command=self.remove_download,
            width=30,
            height=25,
            font=ctk.CTkFont(size=12)
        )
        self.remove_btn.pack(side="left", padx=2)
        
        # Open folder button
        self.open_folder_btn = ctk.CTkButton(
            buttons_frame,
            text="📂",
            command=self.open_folder,
            width=30,
            height=25,
            font=ctk.CTkFont(size=12)
        )
        self.open_folder_btn.pack(side="left", padx=2)
    
    def update_display(self):
        """Update the display based on current download info"""
        status = self.download_info.get('status', 'pending')
        progress = self.download_info.get('progress', 0)
        speed = self.download_info.get('speed', 0)
        size = self.download_info.get('size', 0)
        downloaded = self.download_info.get('downloaded', 0)
        
        # Update progress bar
        self.progress_bar.set(progress / 100.0 if progress > 0 else 0)
        
        # Update status text
        if status == 'pending':
            status_text = "في الانتظار"
        elif status == 'downloading':
            if size > 0:
                size_text = self.format_size(size)
                downloaded_text = self.format_size(downloaded)
                speed_text = self.format_speed(speed)
                status_text = f"جاري التحميل - {downloaded_text}/{size_text} ({speed_text})"
            else:
                speed_text = self.format_speed(speed)
                status_text = f"جاري التحميل - {speed_text}"
        elif status == 'paused':
            if size > 0:
                size_text = self.format_size(size)
                downloaded_text = self.format_size(downloaded)
                status_text = f"متوقف - {downloaded_text}/{size_text}"
            else:
                status_text = "متوقف"
        elif status == 'completed':
            if size > 0:
                size_text = self.format_size(size)
                status_text = f"مكتمل - {size_text}"
            else:
                status_text = "مكتمل"
        elif status == 'error':
            status_text = "خطأ في التحميل"
        else:
            status_text = status
        
        self.status_label.configure(text=status_text)
        
        # Update button states
        if status == 'downloading':
            self.start_pause_btn.configure(text="⏸️")
            self.is_downloading_flag = True
        else:
            self.start_pause_btn.configure(text="▶️")
            self.is_downloading_flag = False
        
        # Update icon based on file type
        filename = self.download_info['filename']
        if filename:
            ext = Path(filename).suffix.lower()
            if ext in config.SUPPORTED_VIDEO_FORMATS:
                self.icon_label.configure(text="🎬")
            elif ext in config.SUPPORTED_AUDIO_FORMATS:
                self.icon_label.configure(text="🎵")
            elif ext in config.SUPPORTED_ARCHIVE_FORMATS:
                self.icon_label.configure(text="📦")
            elif ext in config.SUPPORTED_DOCUMENT_FORMATS:
                self.icon_label.configure(text="📄")
            else:
                self.icon_label.configure(text="📁")
    
    def toggle_download(self):
        """Toggle download start/pause"""
        if self.is_downloading_flag:
            self.pause_download()
        else:
            self.start_download()
    
    def start_download(self):
        """Start the download"""
        if self.download_info['status'] in ['pending', 'paused', 'error']:
            self.download_info['status'] = 'downloading'
            self.is_downloading_flag = True
            
            # Start download in download manager
            self.download_manager.add_download(self.download_info, self.on_progress_update)
            
            self.update_display()
    
    def pause_download(self):
        """Pause the download"""
        if self.download_info['status'] == 'downloading':
            self.download_info['status'] = 'paused'
            self.is_downloading_flag = False
            
            # Pause download in download manager
            self.download_manager.pause_download(self.download_info['id'])
            
            self.update_display()
    
    def stop_download(self):
        """Stop the download"""
        if self.download_info['status'] in ['downloading', 'paused']:
            self.download_info['status'] = 'pending'
            self.download_info['progress'] = 0
            self.download_info['downloaded'] = 0
            self.is_downloading_flag = False
            
            # Stop download in download manager
            self.download_manager.stop_download(self.download_info['id'])
            
            self.update_display()
    
    def remove_download(self):
        """Remove download from list"""
        if messagebox.askyesno("تأكيد", "هل تريد حذف هذا التحميل من القائمة؟"):
            # Stop download if active
            if self.download_info['status'] in ['downloading', 'paused']:
                self.download_manager.stop_download(self.download_info['id'])
            
            # Remove from UI
            self.destroy()
    
    def open_folder(self):
        """Open download folder"""
        import subprocess
        import os
        
        folder_path = self.download_info['save_path']
        if os.path.exists(folder_path):
            if sys.platform == "win32":
                subprocess.run(['explorer', folder_path])
            elif sys.platform == "darwin":
                subprocess.run(['open', folder_path])
            else:
                subprocess.run(['xdg-open', folder_path])
        else:
            messagebox.showwarning("تحذير", "مجلد الحفظ غير موجود")
    
    def on_progress_update(self, progress_info):
        """Handle progress updates from download manager"""
        self.download_info.update(progress_info)
        self.current_speed = progress_info.get('speed', 0)
        
        # Update display in main thread
        self.after(0, self.update_display)
    
    def is_downloading(self):
        """Check if download is active"""
        return self.is_downloading_flag
    
    def is_completed(self):
        """Check if download is completed"""
        return self.download_info.get('status') == 'completed'
    
    def get_speed(self):
        """Get current download speed"""
        return self.current_speed
    
    def format_size(self, bytes_size):
        """Format file size in human readable format"""
        if bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.1f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.1f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.1f} GB"
    
    def format_speed(self, bytes_per_second):
        """Format speed in human readable format"""
        if bytes_per_second < 1024:
            return f"{bytes_per_second:.1f} B/s"
        elif bytes_per_second < 1024 * 1024:
            return f"{bytes_per_second / 1024:.1f} KB/s"
        else:
            return f"{bytes_per_second / (1024 * 1024):.1f} MB/s"
