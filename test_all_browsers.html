<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافات جميع المتصفحات - Python Download Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .browsers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .browser-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            border: 2px solid transparent;
        }
        
        .browser-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .browser-card.chrome {
            border-color: #4285F4;
        }
        
        .browser-card.firefox {
            border-color: #FF6B35;
        }
        
        .browser-card.edge {
            border-color: #0078D4;
        }
        
        .browser-card.chromium {
            border-color: #FF5722;
        }
        
        .browser-icon {
            font-size: 4em;
            margin-bottom: 15px;
            display: block;
        }
        
        .browser-name {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .browser-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 0;
            display: inline-block;
        }
        
        .status-unknown {
            background: rgba(255, 193, 7, 0.3);
            color: #FFC107;
        }
        
        .status-detected {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }
        
        .status-not-detected {
            background: rgba(244, 67, 54, 0.3);
            color: #F44336;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .test-results {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .installation-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .install-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .install-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .install-link.chrome:hover { border-color: #4285F4; }
        .install-link.firefox:hover { border-color: #FF6B35; }
        .install-link.edge:hover { border-color: #0078D4; }
        .install-link.chromium:hover { border-color: #FF5722; }
        
        .youtube-test {
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid #FF0000;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .youtube-link {
            background: #FF0000;
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s;
        }
        
        .youtube-link:hover {
            background: #CC0000;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 اختبار إضافات جميع المتصفحات</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            Python Download Manager - دعم شامل لجميع المتصفحات الرئيسية
        </p>
        
        <div class="browsers-grid">
            <div class="browser-card chrome">
                <span class="browser-icon">🔵</span>
                <div class="browser-name">Google Chrome</div>
                <div class="browser-status status-unknown" id="chrome-status">غير محدد</div>
                <p>الإصدار الأصلي المحسن</p>
                <p><strong>المجلد:</strong> browser_extension/</p>
            </div>
            
            <div class="browser-card firefox">
                <span class="browser-icon">🦊</span>
                <div class="browser-name">Mozilla Firefox</div>
                <div class="browser-status status-unknown" id="firefox-status">غير محدد</div>
                <p>متوافق مع WebExtensions</p>
                <p><strong>المجلد:</strong> browser_extensions/firefox/</p>
            </div>
            
            <div class="browser-card edge">
                <span class="browser-icon">🌊</span>
                <div class="browser-name">Microsoft Edge</div>
                <div class="browser-status status-unknown" id="edge-status">غير محدد</div>
                <p>تكامل مع Windows</p>
                <p><strong>المجلد:</strong> browser_extensions/edge/</p>
            </div>
            
            <div class="browser-card chromium">
                <span class="browser-icon">🌐</span>
                <div class="browser-name">Chromium</div>
                <div class="browser-status status-unknown" id="chromium-status">غير محدد</div>
                <p>مفتوح المصدر</p>
                <p><strong>المجلد:</strong> browser_extensions/chromium/</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 اختبار الكشف عن المتصفح</h2>
            <p>انقر على الزر أدناه لاختبار كشف المتصفح الحالي:</p>
            <button class="test-button" onclick="detectBrowser()">🔍 كشف المتصفح</button>
            <button class="test-button" onclick="testExtensionAPI()">🔧 اختبار API الإضافة</button>
            <button class="test-button" onclick="testServerConnection()">🌐 اختبار الاتصال بالخادم</button>
            <div class="test-results" id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📦 روابط التثبيت السريع</h2>
            <p>انقر على الرابط المناسب لمتصفحك:</p>
            <div class="installation-links">
                <a href="chrome://extensions/" class="install-link chrome" target="_blank">
                    🔵 Chrome Extensions
                </a>
                <a href="about:debugging" class="install-link firefox" target="_blank">
                    🦊 Firefox Debugging
                </a>
                <a href="edge://extensions/" class="install-link edge" target="_blank">
                    🌊 Edge Extensions
                </a>
                <a href="chrome://extensions/" class="install-link chromium" target="_blank">
                    🌐 Chromium Extensions
                </a>
            </div>
        </div>
        
        <div class="youtube-test">
            <h2>🎬 اختبار على YouTube</h2>
            <p>بعد تثبيت الإضافة، اختبرها على فيديو YouTube:</p>
            <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" class="youtube-link" target="_blank">
                🎵 اختبار على فيديو تجريبي
            </a>
            <p style="margin-top: 15px; font-size: 0.9em;">
                ابحث عن أيقونة التحميل في الزاوية العلوية اليسرى من الفيديو
            </p>
        </div>
    </div>

    <script>
        // كشف المتصفح الحالي
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browser = 'unknown';
            let version = 'unknown';
            
            if (userAgent.includes('Firefox/')) {
                browser = 'firefox';
                version = userAgent.match(/Firefox\/(\d+)/)?.[1] || 'unknown';
            } else if (userAgent.includes('Edg/')) {
                browser = 'edge';
                version = userAgent.match(/Edg\/(\d+)/)?.[1] || 'unknown';
            } else if (userAgent.includes('Chrome/')) {
                if (userAgent.includes('Chromium/')) {
                    browser = 'chromium';
                    version = userAgent.match(/Chromium\/(\d+)/)?.[1] || 'unknown';
                } else {
                    browser = 'chrome';
                    version = userAgent.match(/Chrome\/(\d+)/)?.[1] || 'unknown';
                }
            }
            
            // تحديث حالة المتصفح
            updateBrowserStatus(browser);
            
            // عرض النتائج
            const results = document.getElementById('test-results');
            results.textContent = `🔍 المتصفح المكتشف: ${browser}\n📊 الإصدار: ${version}\n🕒 الوقت: ${new Date().toLocaleString('ar')}\n\n`;
            
            // معلومات إضافية
            results.textContent += `📋 معلومات إضافية:\n`;
            results.textContent += `- User Agent: ${userAgent}\n`;
            results.textContent += `- Platform: ${navigator.platform}\n`;
            results.textContent += `- Language: ${navigator.language}\n`;
            results.textContent += `- Online: ${navigator.onLine ? 'متصل' : 'غير متصل'}\n`;
        }
        
        function updateBrowserStatus(detectedBrowser) {
            // إعادة تعيين جميع الحالات
            ['chrome', 'firefox', 'edge', 'chromium'].forEach(browser => {
                const statusEl = document.getElementById(`${browser}-status`);
                statusEl.className = 'browser-status status-not-detected';
                statusEl.textContent = 'غير مكتشف';
            });
            
            // تحديث المتصفح المكتشف
            if (detectedBrowser !== 'unknown') {
                const statusEl = document.getElementById(`${detectedBrowser}-status`);
                statusEl.className = 'browser-status status-detected';
                statusEl.textContent = 'مكتشف ✓';
            }
        }
        
        function testExtensionAPI() {
            const results = document.getElementById('test-results');
            results.textContent = '🔧 اختبار API الإضافة...\n\n';
            
            // اختبار Chrome API
            if (typeof chrome !== 'undefined') {
                results.textContent += '✅ Chrome API متوفر\n';
                if (chrome.runtime) {
                    results.textContent += '✅ chrome.runtime متوفر\n';
                }
                if (chrome.tabs) {
                    results.textContent += '✅ chrome.tabs متوفر\n';
                }
            } else {
                results.textContent += '❌ Chrome API غير متوفر\n';
            }
            
            // اختبار Firefox API
            if (typeof browser !== 'undefined') {
                results.textContent += '✅ Browser API متوفر (Firefox)\n';
                if (browser.runtime) {
                    results.textContent += '✅ browser.runtime متوفر\n';
                }
                if (browser.tabs) {
                    results.textContent += '✅ browser.tabs متوفر\n';
                }
            } else {
                results.textContent += '❌ Browser API غير متوفر\n';
            }
            
            results.textContent += `\n🕒 وقت الاختبار: ${new Date().toLocaleString('ar')}`;
        }
        
        async function testServerConnection() {
            const results = document.getElementById('test-results');
            results.textContent = '🌐 اختبار الاتصال بالخادم...\n\n';
            
            try {
                const response = await fetch('http://localhost:9876/ping', {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    const data = await response.json();
                    results.textContent += '✅ الخادم متصل ويعمل\n';
                    results.textContent += `📊 الحالة: ${data.status}\n`;
                    results.textContent += `💬 الرسالة: ${data.message}\n`;
                } else {
                    results.textContent += `❌ خطأ HTTP: ${response.status}\n`;
                }
            } catch (error) {
                results.textContent += '❌ فشل الاتصال بالخادم\n';
                results.textContent += `🔍 السبب: ${error.message}\n`;
                results.textContent += '💡 تأكد من تشغيل Python Download Manager\n';
            }
            
            results.textContent += `\n🕒 وقت الاختبار: ${new Date().toLocaleString('ar')}`;
        }
        
        // تشغيل الكشف التلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(detectBrowser, 1000);
        });
    </script>
</body>
</html>
