#!/usr/bin/env python3
"""
Test extension server endpoints
"""

import requests
import json

def test_extension_server():
    """Test extension server"""
    base_url = "http://localhost:9876"
    
    print("🧪 Testing Extension Server...")
    
    # Test ping
    try:
        response = requests.get(f"{base_url}/ping", timeout=5)
        if response.status_code == 200:
            print("  ✅ Ping: OK")
            print(f"     Response: {response.json()}")
        else:
            print(f"  ❌ Ping: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Ping: Error - {e}")
        return False
    
    # Test download endpoint
    try:
        test_data = {
            "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "filename": "test_video.mp4",
            "quality": "720p",
            "category": "Videos"
        }
        
        response = requests.post(
            f"{base_url}/download", 
            json=test_data, 
            timeout=10
        )
        
        if response.status_code == 200:
            print("  ✅ Download endpoint: OK")
            result = response.json()
            print(f"     Response: {result}")
        else:
            print(f"  ❌ Download endpoint: Status {response.status_code}")
            print(f"     Error: {response.text}")
    except Exception as e:
        print(f"  ❌ Download endpoint: Error - {e}")
    
    return True

if __name__ == "__main__":
    test_extension_server()
