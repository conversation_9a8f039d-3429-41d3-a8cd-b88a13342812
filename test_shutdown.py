#!/usr/bin/env python3
"""
Test proper shutdown of the application
"""

import sys
import time
import threading
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.core.download_manager import DownloadManager
from src.services.extension_server import ExtensionServer
from src.core.scheduler import DownloadScheduler

def test_shutdown():
    """Test shutdown process"""
    print("🧪 Testing Application Shutdown...")
    
    try:
        # Initialize components
        print("  📦 Initializing components...")
        download_manager = DownloadManager()
        extension_server = ExtensionServer()
        scheduler = DownloadScheduler()
        
        # Start components
        print("  🚀 Starting components...")
        download_manager.start()
        extension_server.start()
        scheduler.start()
        
        print("  ✅ All components started")
        print("  ⏳ Running for 3 seconds...")
        time.sleep(3)
        
        # Test shutdown
        print("  🛑 Testing shutdown...")
        
        # Stop scheduler
        print("    🔄 Stopping scheduler...")
        scheduler.stop()
        print("    ✅ Scheduler stopped")
        
        # Stop extension server
        print("    🌐 Stopping extension server...")
        extension_server.stop()
        print("    ✅ Extension server stopped")
        
        # Stop download manager
        print("    📥 Stopping download manager...")
        download_manager.stop()
        print("    ✅ Download manager stopped")
        
        print("  🎉 Shutdown test completed successfully!")
        return True
        
    except Exception as e:
        print(f"  ❌ Shutdown test failed: {e}")
        return False

def test_forced_shutdown():
    """Test forced shutdown"""
    print("\n🧪 Testing Forced Shutdown...")
    
    def shutdown_after_delay():
        time.sleep(2)
        print("  🔥 Forcing shutdown...")
        import os
        os._exit(0)
    
    # Start shutdown timer
    shutdown_thread = threading.Thread(target=shutdown_after_delay, daemon=True)
    shutdown_thread.start()
    
    print("  ⏳ Waiting for forced shutdown in 2 seconds...")
    time.sleep(5)  # This should not complete
    print("  ❌ Forced shutdown failed!")

if __name__ == "__main__":
    # Test normal shutdown
    success = test_shutdown()
    
    if success:
        print("\n✅ Normal shutdown works correctly!")
        
        # Test forced shutdown
        test_forced_shutdown()
    else:
        print("\n❌ Normal shutdown failed!")
        sys.exit(1)
