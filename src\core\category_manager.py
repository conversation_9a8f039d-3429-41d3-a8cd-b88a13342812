"""
Category Manager for Python Download Manager
Advanced file categorization and organization
"""

import json
import re
from pathlib import Path
import sys
from typing import Dict, List, Optional
from urllib.parse import urlparse

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger

class CategoryRule:
    def __init__(self, name: str, rule_type: str, pattern: str, target_category: str, priority: int = 0):
        self.name = name
        self.rule_type = rule_type  # "extension", "url_domain", "url_pattern", "filename_pattern", "size"
        self.pattern = pattern
        self.target_category = target_category
        self.priority = priority
        self.is_active = True

class CategoryManager:
    def __init__(self):
        self.logger = get_logger(__name__)
        self.categories = {}
        self.rules = []
        
        # Load default categories and rules
        self.load_default_categories()
        self.load_custom_categories()
        self.load_rules()
    
    def load_default_categories(self):
        """Load default file categories"""
        self.categories = {
            "Videos": {
                "name": "فيديوهات",
                "description": "ملفات الفيديو",
                "extensions": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".3gp"],
                "icon": "🎬",
                "color": "#e74c3c",
                "default_path": "Videos"
            },
            "Audio": {
                "name": "صوتيات",
                "description": "ملفات الصوت",
                "extensions": [".mp3", ".wav", ".flac", ".aac", ".ogg", ".m4a", ".wma"],
                "icon": "🎵",
                "color": "#9b59b6",
                "default_path": "Audio"
            },
            "Documents": {
                "name": "مستندات",
                "description": "المستندات والملفات النصية",
                "extensions": [".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx"],
                "icon": "📄",
                "color": "#3498db",
                "default_path": "Documents"
            },
            "Archives": {
                "name": "أرشيف",
                "description": "الملفات المضغوطة",
                "extensions": [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"],
                "icon": "📦",
                "color": "#f39c12",
                "default_path": "Archives"
            },
            "Images": {
                "name": "صور",
                "description": "ملفات الصور",
                "extensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".webp", ".tiff"],
                "icon": "🖼️",
                "color": "#2ecc71",
                "default_path": "Images"
            },
            "Software": {
                "name": "برامج",
                "description": "البرامج والتطبيقات",
                "extensions": [".exe", ".msi", ".dmg", ".pkg", ".deb", ".rpm", ".appimage"],
                "icon": "💿",
                "color": "#34495e",
                "default_path": "Software"
            },
            "Books": {
                "name": "كتب",
                "description": "الكتب الإلكترونية",
                "extensions": [".epub", ".mobi", ".azw", ".azw3", ".fb2"],
                "icon": "📚",
                "color": "#8e44ad",
                "default_path": "Books"
            },
            "Others": {
                "name": "أخرى",
                "description": "ملفات أخرى",
                "extensions": [],
                "icon": "📁",
                "color": "#95a5a6",
                "default_path": "Others"
            }
        }
    
    def load_custom_categories(self):
        """Load custom categories from file"""
        try:
            categories_file = config.CONFIG_DIR / "categories.json"
            
            if categories_file.exists():
                with open(categories_file, 'r', encoding='utf-8') as f:
                    custom_categories = json.load(f)
                
                # Merge with default categories
                self.categories.update(custom_categories)
                
                self.logger.info(f"Loaded {len(custom_categories)} custom categories")
        
        except Exception as e:
            self.logger.error(f"Error loading custom categories: {e}")
    
    def load_rules(self):
        """Load categorization rules"""
        try:
            rules_file = config.CONFIG_DIR / "category_rules.json"
            
            if rules_file.exists():
                with open(rules_file, 'r', encoding='utf-8') as f:
                    rules_data = json.load(f)
                
                self.rules = []
                for rule_data in rules_data:
                    rule = CategoryRule(
                        name=rule_data['name'],
                        rule_type=rule_data['rule_type'],
                        pattern=rule_data['pattern'],
                        target_category=rule_data['target_category'],
                        priority=rule_data.get('priority', 0)
                    )
                    rule.is_active = rule_data.get('is_active', True)
                    self.rules.append(rule)
                
                # Sort rules by priority
                self.rules.sort(key=lambda x: x.priority, reverse=True)
                
                self.logger.info(f"Loaded {len(self.rules)} categorization rules")
            else:
                # Create default rules
                self.create_default_rules()
        
        except Exception as e:
            self.logger.error(f"Error loading categorization rules: {e}")
            self.create_default_rules()
    
    def create_default_rules(self):
        """Create default categorization rules"""
        default_rules = [
            # YouTube videos
            CategoryRule("YouTube Videos", "url_domain", "youtube.com", "Videos", 10),
            CategoryRule("YouTube Shorts", "url_pattern", r"youtube\.com/shorts/", "Videos", 15),
            
            # TikTok videos
            CategoryRule("TikTok Videos", "url_domain", "tiktok.com", "Videos", 10),
            
            # Social media videos
            CategoryRule("Instagram Videos", "url_domain", "instagram.com", "Videos", 8),
            CategoryRule("Twitter Videos", "url_domain", "twitter.com", "Videos", 8),
            
            # Software downloads
            CategoryRule("GitHub Releases", "url_pattern", r"github\.com/.+/releases/", "Software", 12),
            CategoryRule("SourceForge", "url_domain", "sourceforge.net", "Software", 10),
            
            # Document sites
            CategoryRule("Google Drive Docs", "url_domain", "drive.google.com", "Documents", 8),
            CategoryRule("Dropbox Files", "url_domain", "dropbox.com", "Documents", 8),
            
            # Image sites
            CategoryRule("Imgur Images", "url_domain", "imgur.com", "Images", 10),
            CategoryRule("Pinterest Images", "url_domain", "pinterest.com", "Images", 8),
            
            # Book sites
            CategoryRule("Archive.org Books", "url_pattern", r"archive\.org/.+\.(epub|pdf)", "Books", 12),
            
            # Large files (likely videos or software)
            CategoryRule("Large Files", "size", ">100MB", "Videos", 5),
        ]
        
        self.rules = default_rules
        self.save_rules()
    
    def categorize_download(self, download_info: Dict) -> str:
        """Categorize a download based on rules and file info"""
        try:
            url = download_info.get('url', '')
            filename = download_info.get('filename', '')
            file_size = download_info.get('size', 0)
            
            # Apply rules in priority order
            for rule in self.rules:
                if not rule.is_active:
                    continue
                
                if self._apply_rule(rule, url, filename, file_size):
                    self.logger.debug(f"Applied rule '{rule.name}' -> {rule.target_category}")
                    return rule.target_category
            
            # Fallback to extension-based categorization
            category = self._categorize_by_extension(filename)
            if category:
                return category
            
            # Default category
            return "Others"
        
        except Exception as e:
            self.logger.error(f"Error categorizing download: {e}")
            return "Others"
    
    def _apply_rule(self, rule: CategoryRule, url: str, filename: str, file_size: int) -> bool:
        """Apply a categorization rule"""
        try:
            if rule.rule_type == "extension":
                return filename.lower().endswith(rule.pattern.lower())
            
            elif rule.rule_type == "url_domain":
                parsed_url = urlparse(url)
                return rule.pattern.lower() in parsed_url.netloc.lower()
            
            elif rule.rule_type == "url_pattern":
                return bool(re.search(rule.pattern, url, re.IGNORECASE))
            
            elif rule.rule_type == "filename_pattern":
                return bool(re.search(rule.pattern, filename, re.IGNORECASE))
            
            elif rule.rule_type == "size":
                return self._evaluate_size_condition(rule.pattern, file_size)
            
            return False
        
        except Exception as e:
            self.logger.error(f"Error applying rule '{rule.name}': {e}")
            return False
    
    def _evaluate_size_condition(self, condition: str, file_size: int) -> bool:
        """Evaluate size condition (e.g., '>100MB', '<50KB')"""
        try:
            # Parse condition like ">100MB", "<50KB", "=1GB"
            match = re.match(r'([><=]+)(\d+(?:\.\d+)?)(KB|MB|GB)', condition.upper())
            if not match:
                return False
            
            operator, value, unit = match.groups()
            value = float(value)
            
            # Convert to bytes
            multipliers = {'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
            threshold = value * multipliers[unit]
            
            # Apply operator
            if operator == '>':
                return file_size > threshold
            elif operator == '<':
                return file_size < threshold
            elif operator == '>=':
                return file_size >= threshold
            elif operator == '<=':
                return file_size <= threshold
            elif operator == '=':
                return abs(file_size - threshold) < (threshold * 0.1)  # 10% tolerance
            
            return False
        
        except Exception:
            return False
    
    def _categorize_by_extension(self, filename: str) -> Optional[str]:
        """Categorize by file extension"""
        if not filename:
            return None
        
        file_ext = Path(filename).suffix.lower()
        
        for category_id, category_info in self.categories.items():
            if file_ext in category_info.get('extensions', []):
                return category_id
        
        return None
    
    def add_category(self, category_id: str, category_info: Dict) -> bool:
        """Add a new category"""
        try:
            self.categories[category_id] = category_info
            self.save_categories()
            self.logger.info(f"Added category: {category_id}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error adding category: {e}")
            return False
    
    def remove_category(self, category_id: str) -> bool:
        """Remove a category"""
        try:
            if category_id in self.categories:
                del self.categories[category_id]
                self.save_categories()
                self.logger.info(f"Removed category: {category_id}")
                return True
            return False
        
        except Exception as e:
            self.logger.error(f"Error removing category: {e}")
            return False
    
    def add_rule(self, rule: CategoryRule) -> bool:
        """Add a new categorization rule"""
        try:
            self.rules.append(rule)
            self.rules.sort(key=lambda x: x.priority, reverse=True)
            self.save_rules()
            self.logger.info(f"Added rule: {rule.name}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error adding rule: {e}")
            return False
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove a categorization rule"""
        try:
            self.rules = [r for r in self.rules if r.name != rule_name]
            self.save_rules()
            self.logger.info(f"Removed rule: {rule_name}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error removing rule: {e}")
            return False
    
    def get_category_path(self, category_id: str, base_path: str) -> str:
        """Get the full path for a category"""
        category_info = self.categories.get(category_id, {})
        category_path = category_info.get('default_path', category_id)
        return str(Path(base_path) / category_path)
    
    def get_categories(self) -> Dict:
        """Get all categories"""
        return self.categories.copy()
    
    def get_rules(self) -> List[CategoryRule]:
        """Get all rules"""
        return self.rules.copy()
    
    def save_categories(self):
        """Save custom categories to file"""
        try:
            categories_file = config.CONFIG_DIR / "categories.json"
            
            # Only save custom categories (not default ones)
            default_categories = set(config.FILE_CATEGORIES.keys())
            custom_categories = {
                k: v for k, v in self.categories.items()
                if k not in default_categories
            }
            
            with open(categories_file, 'w', encoding='utf-8') as f:
                json.dump(custom_categories, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            self.logger.error(f"Error saving categories: {e}")
    
    def save_rules(self):
        """Save categorization rules to file"""
        try:
            rules_file = config.CONFIG_DIR / "category_rules.json"
            
            rules_data = []
            for rule in self.rules:
                rules_data.append({
                    'name': rule.name,
                    'rule_type': rule.rule_type,
                    'pattern': rule.pattern,
                    'target_category': rule.target_category,
                    'priority': rule.priority,
                    'is_active': rule.is_active
                })
            
            with open(rules_file, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            self.logger.error(f"Error saving rules: {e}")
