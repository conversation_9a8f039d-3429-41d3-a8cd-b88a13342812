"""
Settings Dialog for Python Download Manager
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import json
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

class SettingsDialog:
    def __init__(self, parent):
        self.parent = parent
        self.settings = self.load_settings()
        
        # Create dialog window
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("الإعدادات")
        self.dialog.geometry("500x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        self.create_widgets()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create settings widgets"""
        # Create notebook for tabs
        self.notebook = ctk.CTkTabview(self.dialog)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=20)
        
        # General tab
        self.create_general_tab()
        
        # Downloads tab
        self.create_downloads_tab()
        
        # Network tab
        self.create_network_tab()
        
        # Buttons frame
        button_frame = ctk.CTkFrame(self.dialog)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Cancel button
        self.cancel_btn = ctk.CTkButton(
            button_frame,
            text="إلغاء",
            command=self.cancel,
            width=100
        )
        self.cancel_btn.pack(side="right", padx=15, pady=15)
        
        # Save button
        self.save_btn = ctk.CTkButton(
            button_frame,
            text="حفظ",
            command=self.save_settings,
            width=100
        )
        self.save_btn.pack(side="right", padx=(15, 10), pady=15)
    
    def create_general_tab(self):
        """Create general settings tab"""
        general_tab = self.notebook.add("عام")
        
        # Default download path
        path_frame = ctk.CTkFrame(general_tab)
        path_frame.pack(fill="x", padx=15, pady=15)
        
        ctk.CTkLabel(path_frame, text="مجلد التحميل الافتراضي:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        path_input_frame = ctk.CTkFrame(path_frame)
        path_input_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        self.download_path_var = tk.StringVar(value=self.settings.get('download_path', config.DEFAULT_DOWNLOAD_PATH))
        self.download_path_entry = ctk.CTkEntry(
            path_input_frame,
            textvariable=self.download_path_var
        )
        self.download_path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        self.browse_path_btn = ctk.CTkButton(
            path_input_frame,
            text="تصفح",
            command=self.browse_download_path,
            width=80
        )
        self.browse_path_btn.pack(side="right")
        
        # Theme selection
        theme_frame = ctk.CTkFrame(general_tab)
        theme_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(theme_frame, text="المظهر:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.theme_var = tk.StringVar(value=self.settings.get('theme', 'dark'))
        self.theme_combo = ctk.CTkComboBox(
            theme_frame,
            variable=self.theme_var,
            values=["dark", "light"],
            state="readonly"
        )
        self.theme_combo.pack(fill="x", padx=15, pady=(0, 15))
        
        # Auto-start downloads
        self.auto_start_var = tk.BooleanVar(value=self.settings.get('auto_start', True))
        self.auto_start_check = ctk.CTkCheckBox(
            general_tab,
            text="بدء التحميلات تلقائياً",
            variable=self.auto_start_var
        )
        self.auto_start_check.pack(anchor="w", padx=30, pady=10)
        
        # Show notifications
        self.notifications_var = tk.BooleanVar(value=self.settings.get('show_notifications', True))
        self.notifications_check = ctk.CTkCheckBox(
            general_tab,
            text="إظهار الإشعارات",
            variable=self.notifications_var
        )
        self.notifications_check.pack(anchor="w", padx=30, pady=10)
    
    def create_downloads_tab(self):
        """Create downloads settings tab"""
        downloads_tab = self.notebook.add("التحميلات")
        
        # Max concurrent downloads
        concurrent_frame = ctk.CTkFrame(downloads_tab)
        concurrent_frame.pack(fill="x", padx=15, pady=15)
        
        ctk.CTkLabel(concurrent_frame, text="عدد التحميلات المتزامنة:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.max_downloads_var = tk.IntVar(value=self.settings.get('max_concurrent_downloads', config.MAX_CONCURRENT_DOWNLOADS))
        self.max_downloads_slider = ctk.CTkSlider(
            concurrent_frame,
            from_=1,
            to=10,
            number_of_steps=9,
            variable=self.max_downloads_var
        )
        self.max_downloads_slider.pack(fill="x", padx=15, pady=(0, 10))
        
        self.max_downloads_label = ctk.CTkLabel(
            concurrent_frame,
            text=f"القيمة: {self.max_downloads_var.get()}"
        )
        self.max_downloads_label.pack(padx=15, pady=(0, 15))
        
        # Bind slider change
        self.max_downloads_slider.configure(command=self.update_max_downloads_label)
        
        # Chunk size
        chunk_frame = ctk.CTkFrame(downloads_tab)
        chunk_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(chunk_frame, text="حجم الجزء (KB):", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.chunk_size_var = tk.IntVar(value=self.settings.get('chunk_size', config.DEFAULT_CHUNK_SIZE) // 1024)
        self.chunk_size_entry = ctk.CTkEntry(
            chunk_frame,
            textvariable=self.chunk_size_var,
            width=100
        )
        self.chunk_size_entry.pack(padx=15, pady=(0, 15))
        
        # Max retries
        retries_frame = ctk.CTkFrame(downloads_tab)
        retries_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(retries_frame, text="عدد المحاولات:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.max_retries_var = tk.IntVar(value=self.settings.get('max_retries', config.MAX_RETRIES))
        self.max_retries_entry = ctk.CTkEntry(
            retries_frame,
            textvariable=self.max_retries_var,
            width=100
        )
        self.max_retries_entry.pack(padx=15, pady=(0, 15))
    
    def create_network_tab(self):
        """Create network settings tab"""
        network_tab = self.notebook.add("الشبكة")
        
        # Timeout
        timeout_frame = ctk.CTkFrame(network_tab)
        timeout_frame.pack(fill="x", padx=15, pady=15)
        
        ctk.CTkLabel(timeout_frame, text="مهلة الاتصال (ثانية):", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.timeout_var = tk.IntVar(value=self.settings.get('timeout', config.DEFAULT_TIMEOUT))
        self.timeout_entry = ctk.CTkEntry(
            timeout_frame,
            textvariable=self.timeout_var,
            width=100
        )
        self.timeout_entry.pack(padx=15, pady=(0, 15))
        
        # User Agent
        ua_frame = ctk.CTkFrame(network_tab)
        ua_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(ua_frame, text="User Agent:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.user_agent_var = tk.StringVar(value=self.settings.get('user_agent', config.USER_AGENT))
        self.user_agent_entry = ctk.CTkEntry(
            ua_frame,
            textvariable=self.user_agent_var
        )
        self.user_agent_entry.pack(fill="x", padx=15, pady=(0, 15))
        
        # Proxy settings
        proxy_frame = ctk.CTkFrame(network_tab)
        proxy_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(proxy_frame, text="إعدادات البروكسي:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=15, pady=(15, 5))
        
        self.use_proxy_var = tk.BooleanVar(value=self.settings.get('use_proxy', False))
        self.use_proxy_check = ctk.CTkCheckBox(
            proxy_frame,
            text="استخدام البروكسي",
            variable=self.use_proxy_var,
            command=self.toggle_proxy_settings
        )
        self.use_proxy_check.pack(anchor="w", padx=15, pady=(0, 10))
        
        # Proxy details frame
        self.proxy_details_frame = ctk.CTkFrame(proxy_frame)
        if self.use_proxy_var.get():
            self.proxy_details_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        ctk.CTkLabel(self.proxy_details_frame, text="عنوان البروكسي:").pack(anchor="w", padx=10, pady=(10, 5))
        self.proxy_host_var = tk.StringVar(value=self.settings.get('proxy_host', ''))
        self.proxy_host_entry = ctk.CTkEntry(
            self.proxy_details_frame,
            textvariable=self.proxy_host_var,
            placeholder_text="127.0.0.1"
        )
        self.proxy_host_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        ctk.CTkLabel(self.proxy_details_frame, text="المنفذ:").pack(anchor="w", padx=10, pady=(0, 5))
        self.proxy_port_var = tk.StringVar(value=self.settings.get('proxy_port', ''))
        self.proxy_port_entry = ctk.CTkEntry(
            self.proxy_details_frame,
            textvariable=self.proxy_port_var,
            placeholder_text="8080"
        )
        self.proxy_port_entry.pack(fill="x", padx=10, pady=(0, 10))
    
    def update_max_downloads_label(self, value):
        """Update max downloads label"""
        self.max_downloads_label.configure(text=f"القيمة: {int(value)}")
    
    def toggle_proxy_settings(self):
        """Toggle proxy settings visibility"""
        if self.use_proxy_var.get():
            self.proxy_details_frame.pack(fill="x", padx=15, pady=(0, 15))
        else:
            self.proxy_details_frame.pack_forget()
    
    def browse_download_path(self):
        """Browse for download path"""
        folder = filedialog.askdirectory(
            title="اختر مجلد التحميل الافتراضي",
            initialdir=self.download_path_var.get()
        )
        if folder:
            self.download_path_var.set(folder)
    
    def load_settings(self):
        """Load settings from file"""
        settings_file = config.CONFIG_DIR / "settings.json"
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading settings: {e}")
        
        # Return default settings
        return {
            'download_path': config.DEFAULT_DOWNLOAD_PATH,
            'theme': 'dark',
            'auto_start': True,
            'show_notifications': True,
            'max_concurrent_downloads': config.MAX_CONCURRENT_DOWNLOADS,
            'chunk_size': config.DEFAULT_CHUNK_SIZE,
            'max_retries': config.MAX_RETRIES,
            'timeout': config.DEFAULT_TIMEOUT,
            'user_agent': config.USER_AGENT,
            'use_proxy': False,
            'proxy_host': '',
            'proxy_port': ''
        }
    
    def save_settings(self):
        """Save settings to file"""
        try:
            settings = {
                'download_path': self.download_path_var.get(),
                'theme': self.theme_var.get(),
                'auto_start': self.auto_start_var.get(),
                'show_notifications': self.notifications_var.get(),
                'max_concurrent_downloads': int(self.max_downloads_var.get()),
                'chunk_size': int(self.chunk_size_var.get()) * 1024,
                'max_retries': int(self.max_retries_var.get()),
                'timeout': int(self.timeout_var.get()),
                'user_agent': self.user_agent_var.get(),
                'use_proxy': self.use_proxy_var.get(),
                'proxy_host': self.proxy_host_var.get(),
                'proxy_port': self.proxy_port_var.get()
            }
            
            settings_file = config.CONFIG_DIR / "settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def cancel(self):
        """Cancel and close dialog"""
        self.dialog.destroy()
