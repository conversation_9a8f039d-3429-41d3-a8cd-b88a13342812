#!/usr/bin/env python3
"""
Test format selection feature
"""

import sys
import requests
import json
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

def test_format_endpoint():
    """Test the /formats endpoint"""
    print("🧪 Testing Format Selection Feature...")
    
    try:
        # Test URL
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        print(f"  📹 Testing with URL: {test_url}")
        
        # Send request to get formats
        response = requests.post('http://localhost:9876/formats', 
                               json={'url': test_url},
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success') and data.get('formats'):
                formats = data['formats']
                print(f"  ✅ Found {len(formats)} formats:")
                
                for i, fmt in enumerate(formats[:5]):  # Show first 5
                    quality = fmt.get('quality', 'Unknown')
                    ext = fmt.get('ext', 'mp4')
                    filesize = fmt.get('filesize')
                    size_str = f" ({format_filesize(filesize)})" if filesize else ""
                    
                    print(f"    {i+1}. {quality} - {ext}{size_str}")
                
                if len(formats) > 5:
                    print(f"    ... and {len(formats) - 5} more formats")
                
                return True
            else:
                print(f"  ❌ No formats found: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"  ❌ Server error: {response.status_code}")
            print(f"     Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ Cannot connect to server. Make sure the program is running.")
        return False
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def format_filesize(bytes_size):
    """Format file size"""
    if not bytes_size:
        return "Unknown size"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_size < 1024:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024
    return f"{bytes_size:.1f} TB"

def test_download_with_format():
    """Test downloading with specific format"""
    print("\n🧪 Testing Download with Specific Format...")
    
    try:
        # First get formats
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        response = requests.post('http://localhost:9876/formats', 
                               json={'url': test_url},
                               timeout=30)
        
        if response.status_code != 200:
            print("  ❌ Cannot get formats")
            return False
        
        data = response.json()
        if not data.get('success') or not data.get('formats'):
            print("  ❌ No formats available")
            return False
        
        formats = data['formats']
        if not formats:
            print("  ❌ Empty formats list")
            return False
        
        # Use first format for test
        selected_format = formats[0]
        print(f"  📹 Testing download with format: {selected_format.get('quality', 'Unknown')}")
        
        # Send download request with format
        download_data = {
            'url': test_url,
            'format': selected_format
        }
        
        response = requests.post('http://localhost:9876/download',
                               json=download_data,
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"  ✅ Download request successful: {result.get('message', 'OK')}")
                return True
            else:
                print(f"  ❌ Download failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"  ❌ Server error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def test_server_connection():
    """Test server connection"""
    print("🔗 Testing Server Connection...")
    
    try:
        response = requests.get('http://localhost:9876/ping', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Server is running: {data.get('message', 'OK')}")
            return True
        else:
            print(f"  ❌ Server error: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("  ❌ Cannot connect to server. Make sure the program is running.")
        return False
    except Exception as e:
        print(f"  ❌ Connection test failed: {e}")
        return False

if __name__ == "__main__":
    print("🎬 Format Selection Feature Test")
    print("=" * 50)
    
    # Test server connection
    if not test_server_connection():
        print("\n💥 Server is not running. Please start the program first.")
        sys.exit(1)
    
    # Test format endpoint
    success1 = test_format_endpoint()
    
    # Test download with format
    success2 = test_download_with_format()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All tests PASSED!")
        print("Format selection feature is working correctly!")
    else:
        print("💥 Some tests FAILED!")
        if not success1:
            print("- Format retrieval test failed")
        if not success2:
            print("- Download with format test failed")
    
    print("\n💡 To test the full feature:")
    print("1. Open YouTube in Chrome")
    print("2. Go to any video")
    print("3. Click the download icon")
    print("4. You should see a format selection dialog")
    print("5. Choose a format and click download")
