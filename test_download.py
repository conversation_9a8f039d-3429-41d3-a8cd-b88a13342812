#!/usr/bin/env python3
"""
Test script to verify download functionality
"""

import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_url_validator():
    """Test URL validation"""
    from src.utils.url_validator import URLValidator
    
    print("🧪 Testing URL Validator...")
    
    # Test YouTube URLs
    youtube_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ"
    ]
    
    for url in youtube_urls:
        is_valid = URLValidator.is_valid_url(url)
        is_youtube = URLValidator.is_youtube_url(url)
        print(f"  ✅ {url[:50]}... - Valid: {is_valid}, YouTube: {is_youtube}")
    
    # Test TikTok URLs
    tiktok_urls = [
        "https://www.tiktok.com/@user/video/1234567890",
        "https://vm.tiktok.com/ZMeAbCdEf/"
    ]
    
    for url in tiktok_urls:
        is_valid = URLValidator.is_valid_url(url)
        is_tiktok = URLValidator.is_tiktok_url(url)
        print(f"  ✅ {url[:50]}... - Valid: {is_valid}, TikTok: {is_tiktok}")

def test_extension_server():
    """Test extension server"""
    import requests
    
    print("\n🌐 Testing Extension Server...")
    
    try:
        response = requests.get("http://localhost:9876/ping", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Server is running: {data.get('message', 'OK')}")
        else:
            print(f"  ❌ Server returned status: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("  ❌ Server is not running")
    except Exception as e:
        print(f"  ❌ Error: {e}")

def test_download_manager():
    """Test download manager"""
    from src.core.download_manager import DownloadManager
    
    print("\n📥 Testing Download Manager...")
    
    try:
        dm = DownloadManager()
        print("  ✅ Download Manager created successfully")
        
        # Test adding a simple download
        download_info = {
            'id': 'test-123',
            'url': 'https://httpbin.org/bytes/1024',
            'filename': 'test_file.bin',
            'save_path': './downloads',
            'category': 'Others',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 1024,
            'downloaded': 0
        }
        
        success = dm.add_download(download_info)
        print(f"  ✅ Add download test: {'Success' if success else 'Failed'}")
        
        # Get statistics
        stats = dm.get_statistics()
        print(f"  ✅ Statistics: {stats['total_downloads']} total downloads")
        
    except Exception as e:
        print(f"  ❌ Error: {e}")

def main():
    """Main test function"""
    print("🚀 Python Download Manager - Test Suite")
    print("=" * 50)
    
    test_url_validator()
    test_extension_server()
    test_download_manager()
    
    print("\n" + "=" * 50)
    print("✅ Tests completed!")
    print("\n📋 Instructions for using the GUI:")
    print("1. The main window should be open")
    print("2. Click 'إضافة تحميل' (Add Download) button")
    print("3. Paste a YouTube URL like: https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    print("4. Click 'إضافة للتحميل' (Add to Download) button")
    print("5. The download should appear in the main list")

if __name__ == "__main__":
    main()
