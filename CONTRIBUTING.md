# المساهمة في Python Download Manager

نرحب بمساهماتكم في تطوير Python Download Manager! هذا الدليل سيساعدكم على البدء.

## كيفية المساهمة

### 1. إعداد بيئة التطوير

```bash
# استنساخ المشروع
git clone https://github.com/your-username/python-download-manager.git
cd python-download-manager

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows

# تثبيت المتطلبات
pip install -r requirements.txt

# تثبيت أدوات التطوير
pip install -r requirements-dev.txt
```

### 2. تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
pytest

# تشغيل اختبارات محددة
pytest tests/test_url_validator.py

# تشغيل مع تقرير التغطية
pytest --cov=src --cov-report=html
```

### 3. فحص جودة الكود

```bash
# فحص التنسيق
black --check src/

# تطبيق التنسيق
black src/

# فحص الأخطاء
flake8 src/

# فحص الأنواع
mypy src/
```

## إرشادات المساهمة

### أنواع المساهمات المرحب بها

- 🐛 **إصلاح الأخطاء**: تقارير وإصلاحات للأخطاء
- ✨ **ميزات جديدة**: إضافة وظائف جديدة
- 📚 **التوثيق**: تحسين الوثائق والأدلة
- 🎨 **التصميم**: تحسينات واجهة المستخدم
- ⚡ **الأداء**: تحسينات الأداء والسرعة
- 🧪 **الاختبارات**: إضافة أو تحسين الاختبارات

### عملية المساهمة

1. **Fork المشروع** على GitHub
2. **إنشاء فرع جديد** للميزة أو الإصلاح:
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **إجراء التغييرات** مع اتباع معايير الكود
4. **إضافة اختبارات** للكود الجديد
5. **التأكد من نجاح الاختبارات**:
   ```bash
   pytest
   flake8 src/
   black --check src/
   ```
6. **Commit التغييرات**:
   ```bash
   git commit -m "Add amazing feature"
   ```
7. **Push للفرع**:
   ```bash
   git push origin feature/amazing-feature
   ```
8. **إنشاء Pull Request**

### معايير الكود

#### Python
- استخدم **Python 3.8+**
- اتبع **PEP 8** لتنسيق الكود
- استخدم **Type Hints** حيثما أمكن
- اكتب **Docstrings** للدوال والفئات
- أضف **اختبارات** للكود الجديد

#### التسمية
- استخدم أسماء وصفية للمتغيرات والدوال
- استخدم `snake_case` للدوال والمتغيرات
- استخدم `PascalCase` للفئات
- استخدم `UPPER_CASE` للثوابت

#### التعليقات
- اكتب التعليقات باللغة العربية أو الإنجليزية
- اشرح **لماذا** وليس **ماذا**
- حدث التعليقات عند تغيير الكود

### هيكل المشروع

```
python_download/
├── src/                    # الكود المصدري
│   ├── core/              # المحرك الأساسي
│   ├── gui/               # واجهة المستخدم
│   ├── downloaders/       # محركات التحميل
│   ├── services/          # الخدمات
│   └── utils/             # الأدوات المساعدة
├── tests/                 # الاختبارات
├── browser_extension/     # إضافة المتصفح
├── docs/                  # الوثائق
└── scripts/               # سكريبت مساعدة
```

### إضافة ميزات جديدة

#### 1. محركات تحميل جديدة
لإضافة دعم موقع جديد:

1. أنشئ ملف في `src/downloaders/`
2. اتبع نمط `youtube_downloader.py`
3. أضف اختبارات في `tests/`
4. حدث `config.py` إذا لزم الأمر

#### 2. واجهات مستخدم جديدة
لإضافة نوافذ جديدة:

1. أنشئ ملف في `src/gui/`
2. استخدم `customtkinter` للتصميم
3. اتبع نمط النوافذ الموجودة
4. أضف الترجمة العربية

#### 3. خدمات جديدة
لإضافة خدمات جديدة:

1. أنشئ ملف في `src/services/`
2. استخدم threading للخدمات الخلفية
3. أضف logging مناسب
4. اكتب اختبارات شاملة

### الإبلاغ عن الأخطاء

عند الإبلاغ عن خطأ، يرجى تضمين:

- **وصف واضح** للمشكلة
- **خطوات إعادة الإنتاج**
- **السلوك المتوقع** مقابل **السلوك الفعلي**
- **معلومات النظام**:
  - نظام التشغيل
  - إصدار Python
  - إصدار البرنامج
- **رسائل الخطأ** كاملة
- **لقطات شاشة** إذا كان مناسباً

### طلب ميزات جديدة

عند طلب ميزة جديدة:

- **اشرح المشكلة** التي تحلها الميزة
- **وصف الحل المقترح** بالتفصيل
- **اذكر البدائل** التي فكرت فيها
- **أضف أمثلة** أو mockups إذا أمكن

## الدعم

- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للأسئلة والمناقشات
- **Wiki**: للوثائق المفصلة

## الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع (MIT License).

## شكر خاص

شكراً لجميع المساهمين الذين يساعدون في تطوير هذا المشروع! 🙏

---

**ملاحظة**: هذا المشروع يتبع [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). بالمشاركة، من المتوقع أن تلتزم بهذا الميثاق.
