# دليل ميزة التعامل مع الملفات المكررة

## 🎉 **تم إضافة ميزة التعامل مع الملفات المكررة!**

### ✅ **الميزة الجديدة:**

عندما تحاول تحميل ملف بنفس اسم ملف موجود مسبقاً، ستظهر نافذة خيارات تتيح لك:

#### **🔄 الخيارات المتاحة:**
1. **إعادة تسمية تلقائية** - إضافة رقم للاسم (مثل: video (1).mp4)
2. **استبدال الملف الموجود** - حذف القديم وتحميل الجديد
3. **اختيار اسم مخصص** - كتابة اسم جديد بنفسك
4. **تخطي التحميل** - إلغاء التحميل والاحتفاظ بالملف القديم

### 🎯 **كيف تعمل الميزة:**

#### **عند حدوث تضارب:**
1. **يتوقف التحميل** مؤقتاً
2. **تظهر نافذة حوار** تعرض:
   - معلومات الملف الموجود (الاسم، الحجم، تاريخ التعديل)
   - معلومات الملف الجديد (الاسم، الحجم المتوقع)
   - خيارات التعامل مع التضارب
3. **تختار الخيار المناسب**
4. **يستكمل التحميل** حسب اختيارك

### 🖼️ **واجهة النافذة:**

#### **المعلومات المعروضة:**
- **📁 الملف الموجود:**
  - الاسم الكامل
  - الحجم الحالي
  - تاريخ آخر تعديل

- **📥 الملف الجديد:**
  - الاسم المقترح
  - الحجم المتوقع (إن وُجد)

#### **الخيارات التفاعلية:**
- **🔘 أزرار اختيار** لكل خيار
- **📝 حقل نص** لإدخال اسم مخصص (عند الحاجة)
- **📂 زر تصفح** لاختيار موقع جديد
- **✅ موافق** و **❌ إلغاء**

### 🎬 **أمثلة الاستخدام:**

#### **المثال 1: فيديو YouTube مكرر**
```
الملف الموجود: "Amazing Video.mp4" (45 MB)
الملف الجديد: "Amazing Video.mp4" (50 MB)

الخيارات:
☑️ إعادة تسمية → "Amazing Video (1).mp4"
⚪ استبدال → حذف القديم وتحميل الجديد
⚪ اسم مخصص → "Amazing Video HD.mp4"
⚪ تخطي → إلغاء التحميل
```

#### **المثال 2: ملف عادي مكرر**
```
الملف الموجود: "document.pdf" (2 MB)
الملف الجديد: "document.pdf" (2.5 MB)

الخيارات:
⚪ إعادة تسمية → "document (1).pdf"
☑️ استبدال → تحديث الملف
⚪ اسم مخصص → "document_updated.pdf"
⚪ تخطي → الاحتفاظ بالقديم
```

### 🔧 **التفاصيل التقنية:**

#### **الملفات المضافة:**
- `src/gui/duplicate_file_dialog.py` - نافذة الحوار الرئيسية
- تحديثات في `src/core/downloader.py` - معالجة التضارب
- تحديثات في `src/core/download_manager.py` - ربط GUI

#### **التكامل مع النظام:**
- **تحميلات الفيديو**: YouTube, TikTok
- **التحميلات العادية**: جميع أنواع الملفات
- **الإضافة**: تعمل مع تحميلات الإضافة
- **الواجهة الرئيسية**: تعمل مع التحميلات اليدوية

### 🎯 **السيناريوهات المختلفة:**

#### **1. مع واجهة GUI:**
- تظهر نافذة حوار كاملة
- جميع الخيارات متاحة
- تفاعل كامل مع المستخدم

#### **2. بدون واجهة GUI:**
- إعادة تسمية تلقائية
- لا تحتاج تدخل المستخدم
- مناسب للتشغيل في الخلفية

#### **3. من الإضافة:**
- تظهر النافذة في المتصفح
- خيارات كاملة
- تحديث فوري للحالة

### 💡 **نصائح الاستخدام:**

#### **للحصول على أفضل تجربة:**
1. **اقرأ المعلومات** قبل الاختيار
2. **قارن الأحجام** لمعرفة الملف الأحدث
3. **استخدم أسماء وصفية** عند الاختيار المخصص
4. **فكر في التنظيم** قبل اختيار الموقع

#### **الخيارات الموصى بها:**
- **للفيديوهات**: إعادة تسمية (للاحتفاظ بالنسختين)
- **للوثائق**: استبدال (للحصول على الأحدث)
- **للملفات المهمة**: اسم مخصص (للوضوح)

### 🚀 **الحالة الحالية:**

**🟢 الميزة جاهزة ومُفعلة:**
- ✅ نافذة حوار جميلة ومتجاوبة
- ✅ جميع الخيارات تعمل
- ✅ تكامل كامل مع النظام
- ✅ دعم جميع أنواع التحميلات
- ✅ معالجة الأخطاء

### 🧪 **اختبار الميزة:**

#### **للاختبار:**
1. **شغل البرنامج**: `python main.py`
2. **حمل ملف** أو فيديو
3. **حاول تحميل نفس الملف مرة أخرى**
4. **ستظهر نافذة الخيارات** ✅

#### **أو اختبر النافذة مباشرة:**
```bash
python test_duplicate_dialog.py
```

### 🎊 **المميزات الإضافية:**

#### **تصميم متقدم:**
- **واجهة عربية** كاملة
- **ألوان متناسقة** مع البرنامج
- **أيقونات واضحة** لكل خيار
- **تخطيط منظم** وسهل القراءة

#### **وظائف ذكية:**
- **مقارنة الأحجام** تلقائياً
- **اقتراح أسماء** ذكية
- **حفظ المسار** المختار
- **تذكر الخيارات** (قريباً)

### 🎉 **النتيجة:**

**الآن لن تواجه مشكلة الملفات المكررة!**

- 🎯 **تحكم كامل** في كيفية التعامل مع التضارب
- 🔄 **خيارات متنوعة** لكل حالة
- 💡 **واجهة ذكية** تساعدك في الاختيار
- ⚡ **سرعة في القرار** بدون تعقيد

**استمتع بتحميل منظم وخالي من المشاكل!** 🚀
