#!/usr/bin/env python3
"""
Test duplicate file handling fix
"""

import sys
import time
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.core.download_manager import DownloadManager
import uuid

def test_duplicate_handling():
    """Test duplicate file handling"""
    print("🧪 Testing Duplicate File Handling...")
    
    try:
        # Create test directory
        test_dir = Path("downloads/test")
        test_dir.mkdir(parents=True, exist_ok=True)
        
        # Create existing file
        existing_file = test_dir / "test_file.txt"
        existing_file.write_text("This is the existing file content")
        print(f"  📁 Created existing file: {existing_file}")
        
        # Initialize download manager
        print("  📦 Initializing download manager...")
        download_manager = DownloadManager()
        download_manager.start()
        
        # Create download info for same filename
        download_info = {
            'id': str(uuid.uuid4()),
            'url': 'https://httpbin.org/json',  # Small JSON file
            'filename': 'test_file.txt',
            'save_path': str(test_dir),
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  🚀 Starting download with duplicate filename...")
        success = download_manager.add_download(download_info)
        
        if not success:
            print("  ❌ Failed to start download")
            return False
        
        print("  ⏳ Waiting for download to complete...")
        time.sleep(5)
        
        # Check results
        files_in_dir = list(test_dir.glob("test_file*"))
        print(f"  📊 Files found: {[f.name for f in files_in_dir]}")
        
        if len(files_in_dir) >= 2:
            print("  ✅ Duplicate handling worked - multiple files exist")
            success = True
        elif len(files_in_dir) == 1:
            # Check if file was replaced (size should be different)
            if files_in_dir[0].stat().st_size != len("This is the existing file content"):
                print("  ✅ File was replaced successfully")
                success = True
            else:
                print("  ❌ Download seems to have been skipped or failed")
                success = False
        else:
            print("  ❌ No files found - something went wrong")
            success = False
        
        # Stop download manager
        download_manager.stop()
        
        # Cleanup
        for file in files_in_dir:
            try:
                file.unlink()
            except:
                pass
        
        return success
        
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def test_simple_dialog():
    """Test the simple duplicate dialog"""
    print("\n🧪 Testing Simple Duplicate Dialog...")
    
    try:
        from src.gui.simple_duplicate_dialog import show_duplicate_choice
        
        # Create test file
        test_file = Path("test_existing.txt")
        test_file.write_text("test content")
        
        print("  📋 Showing duplicate dialog...")
        print("  💡 Choose an option in the dialog that appears...")
        
        result = show_duplicate_choice(str(test_file), "test_existing.txt")
        
        print(f"  📊 Dialog result: {result}")
        
        # Cleanup
        test_file.unlink()
        
        if result in ["replace", "rename", "skip"]:
            print("  ✅ Dialog worked correctly")
            return True
        else:
            print("  ❌ Unexpected dialog result")
            return False
            
    except Exception as e:
        print(f"  ❌ Dialog test failed: {e}")
        return False

if __name__ == "__main__":
    # Test duplicate handling
    success1 = test_duplicate_handling()
    
    # Test dialog
    success2 = test_simple_dialog()
    
    if success1 and success2:
        print("\n✅ All tests PASSED!")
        print("Duplicate file handling is working correctly!")
    else:
        print("\n❌ Some tests FAILED!")
        if not success1:
            print("- Duplicate handling test failed")
        if not success2:
            print("- Dialog test failed")
