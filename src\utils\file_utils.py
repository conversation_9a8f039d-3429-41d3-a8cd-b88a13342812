"""
File utilities for Python Download Manager
"""

import os
import re
import mimetypes
from pathlib import Path
from urllib.parse import urlparse, unquote
import requests
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config

def get_filename_from_url(url, default_name="download"):
    """Extract filename from URL"""
    if not url:
        return default_name
    
    try:
        # First try to get filename from URL path
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        
        if path and path != '/':
            filename = os.path.basename(path)
            if filename and '.' in filename:
                return sanitize_filename(filename)
        
        # Try to get filename from server headers
        try:
            response = requests.head(url, timeout=10, allow_redirects=True)
            
            # Check Content-Disposition header
            content_disposition = response.headers.get('Content-Disposition')
            if content_disposition:
                filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
                if filename_match:
                    filename = filename_match.group(1).strip('"\'')
                    return sanitize_filename(filename)
            
            # Try to guess extension from Content-Type
            content_type = response.headers.get('Content-Type')
            if content_type:
                extension = mimetypes.guess_extension(content_type.split(';')[0])
                if extension:
                    return f"{default_name}{extension}"
        
        except:
            pass
        
        # If all else fails, try to extract from URL
        if parsed_url.path:
            path_parts = parsed_url.path.strip('/').split('/')
            if path_parts and path_parts[-1]:
                potential_filename = path_parts[-1]
                if '.' in potential_filename:
                    return sanitize_filename(potential_filename)
        
        return default_name
        
    except:
        return default_name

def sanitize_filename(filename):
    """Sanitize filename for safe file system usage"""
    if not filename:
        return "download"
    
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove control characters
    filename = ''.join(char for char in filename if ord(char) >= 32)
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        max_name_length = 255 - len(ext)
        filename = name[:max_name_length] + ext
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Ensure it's not empty
    if not filename:
        filename = "download"
    
    return filename

def get_file_size(file_path):
    """Get file size in bytes"""
    try:
        return os.path.getsize(file_path)
    except:
        return 0

def get_file_size_from_url(url, timeout=10):
    """Get file size from URL"""
    try:
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        content_length = response.headers.get('Content-Length')
        if content_length:
            return int(content_length)
    except:
        pass
    return 0

def format_file_size(bytes_size):
    """Format file size in human readable format"""
    if bytes_size < 1024:
        return f"{bytes_size} B"
    elif bytes_size < 1024 * 1024:
        return f"{bytes_size / 1024:.1f} KB"
    elif bytes_size < 1024 * 1024 * 1024:
        return f"{bytes_size / (1024 * 1024):.1f} MB"
    else:
        return f"{bytes_size / (1024 * 1024 * 1024):.1f} GB"

def get_file_extension(filename):
    """Get file extension"""
    if not filename:
        return ""
    return Path(filename).suffix.lower()

def get_file_category(filename):
    """Determine file category based on extension"""
    if not filename:
        return "Others"
    
    ext = get_file_extension(filename)
    
    for category, extensions in config.FILE_CATEGORIES.items():
        if ext in extensions:
            return category
    
    return "Others"

def is_valid_filename(filename):
    """Check if filename is valid"""
    if not filename:
        return False
    
    # Check for invalid characters
    invalid_chars = '<>:"/\\|?*'
    if any(char in filename for char in invalid_chars):
        return False
    
    # Check for reserved names (Windows)
    reserved_names = [
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    ]
    
    name_without_ext = Path(filename).stem.upper()
    if name_without_ext in reserved_names:
        return False
    
    return True

def ensure_unique_filename(file_path):
    """Ensure filename is unique by adding number suffix if needed"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        return str(file_path)
    
    base_name = file_path.stem
    extension = file_path.suffix
    parent_dir = file_path.parent
    
    counter = 1
    while True:
        new_name = f"{base_name} ({counter}){extension}"
        new_path = parent_dir / new_name
        
        if not new_path.exists():
            return str(new_path)
        
        counter += 1

def create_directory(directory_path):
    """Create directory if it doesn't exist"""
    try:
        Path(directory_path).mkdir(parents=True, exist_ok=True)
        return True
    except:
        return False

def is_directory_writable(directory_path):
    """Check if directory is writable"""
    try:
        test_file = Path(directory_path) / ".write_test"
        test_file.touch()
        test_file.unlink()
        return True
    except:
        return False

def get_available_space(directory_path):
    """Get available disk space in bytes"""
    try:
        import shutil
        return shutil.disk_usage(directory_path).free
    except:
        return 0

def clean_temp_files(temp_dir):
    """Clean temporary files"""
    try:
        temp_path = Path(temp_dir)
        if temp_path.exists():
            for file_path in temp_path.glob("*"):
                try:
                    if file_path.is_file():
                        file_path.unlink()
                    elif file_path.is_dir():
                        import shutil
                        shutil.rmtree(file_path)
                except:
                    pass
    except:
        pass

def get_file_hash(file_path, algorithm='md5'):
    """Calculate file hash"""
    import hashlib
    
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    except:
        return None

def compare_files(file1, file2):
    """Compare two files"""
    try:
        # First compare sizes
        if get_file_size(file1) != get_file_size(file2):
            return False
        
        # Then compare hashes
        hash1 = get_file_hash(file1)
        hash2 = get_file_hash(file2)
        
        return hash1 == hash2
    except:
        return False

def get_mime_type(filename):
    """Get MIME type of file"""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or 'application/octet-stream'

def is_media_file(filename):
    """Check if file is a media file"""
    if not filename:
        return False
    
    ext = get_file_extension(filename)
    media_extensions = (
        config.SUPPORTED_VIDEO_FORMATS + 
        config.SUPPORTED_AUDIO_FORMATS
    )
    
    return ext in media_extensions

def is_archive_file(filename):
    """Check if file is an archive"""
    if not filename:
        return False
    
    ext = get_file_extension(filename)
    return ext in config.SUPPORTED_ARCHIVE_FORMATS

def get_file_info(file_path):
    """Get comprehensive file information"""
    try:
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        stat = file_path.stat()
        
        return {
            'name': file_path.name,
            'size': stat.st_size,
            'size_formatted': format_file_size(stat.st_size),
            'extension': file_path.suffix.lower(),
            'category': get_file_category(file_path.name),
            'mime_type': get_mime_type(file_path.name),
            'created': stat.st_ctime,
            'modified': stat.st_mtime,
            'is_media': is_media_file(file_path.name),
            'is_archive': is_archive_file(file_path.name)
        }
    except:
        return None
