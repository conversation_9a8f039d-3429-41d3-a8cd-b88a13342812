/* Popup styles for Python Download Manager extension */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
    direction: rtl;
}

.container {
    width: 380px;
    min-height: 400px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 32px;
    height: 32px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status {
    padding: 20px;
    text-align: center;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #666;
}

.loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.main-content {
    padding: 20px;
}

.url-section {
    margin-bottom: 20px;
}

.url-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.input-group {
    display: flex;
    gap: 8px;
}

.input-group input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

.video-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    border-right: 4px solid #667eea;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item strong {
    color: #555;
    min-width: 60px;
}

.info-item span {
    color: #333;
    text-align: left;
    flex: 1;
}

.options-section {
    margin-bottom: 20px;
}

.option-group {
    margin-bottom: 16px;
}

.option-group:last-child {
    margin-bottom: 0;
}

.option-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #555;
}

.option-group select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.2s;
}

.option-group select:focus {
    outline: none;
    border-color: #667eea;
}

.actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.btn-primary {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: transform 0.1s, box-shadow 0.2s;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-secondary {
    background: #f8f9fa;
    color: #555;
    border: 2px solid #e0e0e0;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s, border-color 0.2s;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #ced4da;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    font-size: 14px;
    cursor: pointer;
    text-decoration: underline;
    padding: 8px 0;
}

.btn-link:hover {
    color: #5a6fd8;
}

.quick-actions {
    text-align: center;
    padding-top: 12px;
    border-top: 1px solid #e0e0e0;
}

.error-content {
    padding: 40px 20px;
    text-align: center;
}

.error-message {
    margin-bottom: 24px;
}

.error-message svg {
    color: #dc3545;
    margin-bottom: 12px;
}

.error-message p {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.error-message small {
    color: #666;
    font-size: 12px;
}

.footer {
    background: #f8f9fa;
    padding: 12px 20px;
    border-top: 1px solid #e0e0e0;
}

.links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.links a {
    color: #667eea;
    text-decoration: none;
    font-size: 13px;
    transition: color 0.2s;
}

.links a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .input-group {
        flex-direction: column;
    }
}

/* Animation for content transitions */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success message */
.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.success-message svg {
    color: #28a745;
    flex-shrink: 0;
}

/* Warning message */
.warning-message {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.warning-message svg {
    color: #ffc107;
    flex-shrink: 0;
}
