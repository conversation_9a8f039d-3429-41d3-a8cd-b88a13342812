#!/usr/bin/env python3
"""
اختبار عرض جميع الصيغ المتاحة
"""

import requests
import json

SERVER_URL = "http://localhost:9876"
TEST_URL = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"

def test_formats_display():
    """اختبار عرض جميع الصيغ"""
    print("🧪 اختبار عرض جميع الصيغ المتاحة")
    print("=" * 50)
    
    try:
        # طلب الصيغ
        response = requests.post(
            f"{SERVER_URL}/formats",
            json={"url": TEST_URL},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                formats = data.get('formats', [])
                cached = data.get('cached', False)
                
                print(f"✅ تم الحصول على {len(formats)} صيغة")
                print(f"📦 حالة التخزين: {'مخزن مؤقتاً' if cached else 'جديد'}")
                print("\n📊 تفاصيل الصيغ:")
                print("-" * 80)
                
                # تصنيف الصيغ
                video_formats = []
                audio_formats = []
                mixed_formats = []
                
                for fmt in formats:
                    height = fmt.get('height', 0)
                    acodec = fmt.get('acodec', 'none')
                    vcodec = fmt.get('vcodec', 'none')
                    
                    if height > 0 and acodec != 'none':
                        mixed_formats.append(fmt)
                    elif height > 0:
                        video_formats.append(fmt)
                    elif acodec != 'none':
                        audio_formats.append(fmt)
                
                # عرض الصيغ المختلطة
                if mixed_formats:
                    print(f"\n🎬 صيغ مختلطة (فيديو + صوت): {len(mixed_formats)}")
                    for fmt in mixed_formats:
                        size = fmt.get('filesize')
                        size_str = f"{size/1024/1024:.1f}MB" if size else "غير معروف"
                        print(f"   🎥 {fmt['quality']} - {fmt['ext']} - {size_str} - {fmt['format_id']}")
                
                # عرض الصيغ المرئية فقط
                if video_formats:
                    print(f"\n📹 صيغ فيديو فقط: {len(video_formats)}")
                    for fmt in video_formats:
                        size = fmt.get('filesize')
                        size_str = f"{size/1024/1024:.1f}MB" if size else "غير معروف"
                        print(f"   🎦 {fmt['quality']} - {fmt['ext']} - {size_str} - {fmt['format_id']}")
                
                # عرض الصيغ الصوتية فقط
                if audio_formats:
                    print(f"\n🎵 صيغ صوت فقط: {len(audio_formats)}")
                    for fmt in audio_formats:
                        size = fmt.get('filesize')
                        size_str = f"{size/1024/1024:.1f}MB" if size else "غير معروف"
                        tbr = fmt.get('tbr', 0)
                        print(f"   🎶 {fmt['quality']} - {fmt['ext']} - {size_str} - {tbr}kbps - {fmt['format_id']}")
                
                print(f"\n📈 إجمالي الصيغ المتاحة: {len(formats)}")
                print(f"   🎬 مختلطة: {len(mixed_formats)}")
                print(f"   📹 فيديو فقط: {len(video_formats)}")
                print(f"   🎵 صوت فقط: {len(audio_formats)}")
                
                # تحليل الجودات المتاحة
                qualities = set()
                for fmt in formats:
                    if fmt.get('height'):
                        qualities.add(f"{fmt['height']}p")
                
                if qualities:
                    sorted_qualities = sorted(qualities, key=lambda x: int(x.replace('p', '')), reverse=True)
                    print(f"\n🎯 الجودات المتاحة: {', '.join(sorted_qualities)}")
                
                return True
                
            else:
                print(f"❌ فشل: {data.get('error', 'خطأ غير معروف')}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_server_connection():
    """اختبار الاتصال بالخادم"""
    print("🔌 اختبار الاتصال بالخادم...")
    
    try:
        response = requests.get(f"{SERVER_URL}/ping", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok':
                print("✅ الخادم متصل ويعمل")
                return True
            else:
                print(f"⚠️ الخادم يرد لكن بحالة غير طبيعية: {data}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار عرض جميع الصيغ المتاحة")
    print("=" * 60)
    
    # اختبار الاتصال أولاً
    if not test_server_connection():
        print("\n❌ لا يمكن الاتصال بالخادم. تأكد من تشغيل البرنامج أولاً.")
        return
    
    # اختبار عرض الصيغ
    success = test_formats_display()
    
    if success:
        print("\n🎉 الاختبار نجح! جميع الصيغ تظهر بشكل صحيح.")
        print("\n💡 نصائح:")
        print("- الصيغ المختلطة (فيديو + صوت) هي الأفضل للتحميل المباشر")
        print("- الصيغ المنفصلة (فيديو فقط) تحتاج دمج مع الصوت")
        print("- الصيغ الصوتية مفيدة لتحميل الموسيقى فقط")
    else:
        print("\n❌ الاختبار فشل. تحقق من حالة الخادم والاتصال.")

if __name__ == "__main__":
    main()
