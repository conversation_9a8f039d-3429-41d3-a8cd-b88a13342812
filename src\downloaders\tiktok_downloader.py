"""
TikTok Video Downloader using yt-dlp
"""

import yt_dlp
import os
import sys
from pathlib import Path
import json
import requests
import re

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

import config
from src.core.logger import get_logger
from src.utils.file_utils import sanitize_filename

class TikTokDownloader:
    def __init__(self, progress_callback=None):
        self.progress_callback = progress_callback
        self.logger = get_logger(__name__)
        self.is_cancelled = False
        
        # yt-dlp options for TikTok
        self.ydl_opts = {
            'format': 'best',
            'outtmpl': '%(title)s.%(ext)s',
            'ignoreerrors': True,
            'no_warnings': True,
        }
    
    def get_video_info(self, url):
        """Get TikTok video information"""
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                
                if not info:
                    return None
                
                # Extract relevant information
                video_info = {
                    'id': info.get('id'),
                    'title': info.get('title') or info.get('description', ''),
                    'description': info.get('description'),
                    'duration': info.get('duration'),
                    'uploader': info.get('uploader') or info.get('creator'),
                    'upload_date': info.get('upload_date'),
                    'view_count': info.get('view_count'),
                    'like_count': info.get('like_count'),
                    'comment_count': info.get('comment_count'),
                    'thumbnail': info.get('thumbnail'),
                    'webpage_url': info.get('webpage_url'),
                    'formats': []
                }
                
                # Extract available formats
                if 'formats' in info:
                    for fmt in info['formats']:
                        format_info = {
                            'format_id': fmt.get('format_id'),
                            'ext': fmt.get('ext'),
                            'width': fmt.get('width'),
                            'height': fmt.get('height'),
                            'fps': fmt.get('fps'),
                            'vcodec': fmt.get('vcodec'),
                            'acodec': fmt.get('acodec'),
                            'filesize': fmt.get('filesize'),
                            'quality': self._get_quality_label(fmt)
                        }
                        video_info['formats'].append(format_info)
                
                return video_info
                
        except Exception as e:
            self.logger.error(f"Error getting TikTok video info: {e}")
            return None
    
    def _get_quality_label(self, format_info):
        """Get quality label for format"""
        height = format_info.get('height')
        if height:
            if height >= 1080:
                return 'HD'
            elif height >= 720:
                return 'SD'
            else:
                return 'Low'
        return 'Unknown'
    
    def download_video(self, url, output_path, quality='best', watermark=True):
        """Download TikTok video"""
        try:
            self.is_cancelled = False
            
            # Setup output template
            output_template = os.path.join(output_path, '%(title)s.%(ext)s')
            
            # Configure yt-dlp options
            ydl_opts = self.ydl_opts.copy()
            ydl_opts['outtmpl'] = output_template
            
            # Set quality/format
            if quality == 'best':
                ydl_opts['format'] = 'best'
            elif quality == 'worst':
                ydl_opts['format'] = 'worst'
            else:
                ydl_opts['format'] = quality
            
            # Add progress hook
            ydl_opts['progress_hooks'] = [self._progress_hook]
            
            # Download
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            return True
            
        except Exception as e:
            if not self.is_cancelled:
                self.logger.error(f"Error downloading TikTok video: {e}")
            return False
    
    def download_without_watermark(self, url, output_path):
        """Download TikTok video without watermark (when possible)"""
        try:
            # This is a more complex operation that might require
            # additional processing or alternative methods
            return self.download_video(url, output_path, quality='best')
            
        except Exception as e:
            self.logger.error(f"Error downloading without watermark: {e}")
            return False
    
    def _progress_hook(self, d):
        """Progress hook for yt-dlp"""
        if self.is_cancelled:
            raise yt_dlp.DownloadError("Download cancelled by user")
        
        if self.progress_callback:
            status = d.get('status')
            
            if status == 'downloading':
                progress_info = {
                    'status': 'downloading',
                    'filename': d.get('filename', ''),
                    'downloaded_bytes': d.get('downloaded_bytes', 0),
                    'total_bytes': d.get('total_bytes') or d.get('total_bytes_estimate', 0),
                    'speed': d.get('speed', 0),
                    'eta': d.get('eta', 0)
                }
                
                # Calculate progress percentage
                if progress_info['total_bytes'] > 0:
                    progress_info['progress'] = (progress_info['downloaded_bytes'] / progress_info['total_bytes']) * 100
                else:
                    progress_info['progress'] = 0
                
                self.progress_callback(progress_info)
                
            elif status == 'finished':
                progress_info = {
                    'status': 'completed',
                    'filename': d.get('filename', ''),
                    'total_bytes': d.get('total_bytes', 0),
                    'progress': 100
                }
                self.progress_callback(progress_info)
    
    def cancel_download(self):
        """Cancel ongoing download"""
        self.is_cancelled = True
        self.logger.info("TikTok download cancelled")
    
    def get_available_qualities(self, url):
        """Get available video qualities"""
        try:
            video_info = self.get_video_info(url)
            if not video_info or 'formats' not in video_info:
                return ['best', 'worst']
            
            qualities = set()
            for fmt in video_info['formats']:
                quality = fmt.get('quality')
                if quality and quality != 'Unknown':
                    qualities.add(quality)
            
            available_qualities = list(qualities)
            
            # Add generic options
            if 'best' not in available_qualities:
                available_qualities.insert(0, 'best')
            if 'worst' not in available_qualities:
                available_qualities.append('worst')
            
            return available_qualities
            
        except Exception as e:
            self.logger.error(f"Error getting available qualities: {e}")
            return ['best', 'worst']
    
    def extract_audio(self, url, output_path, audio_format='mp3'):
        """Extract audio from TikTok video"""
        try:
            self.is_cancelled = False
            
            # Setup output template
            output_template = os.path.join(output_path, '%(title)s.%(ext)s')
            
            # Configure yt-dlp options for audio extraction
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': output_template,
                'extractaudio': True,
                'audioformat': audio_format,
                'audioquality': '192K',
                'progress_hooks': [self._progress_hook],
                'ignoreerrors': True,
            }
            
            # Download and extract audio
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            return True
            
        except Exception as e:
            if not self.is_cancelled:
                self.logger.error(f"Error extracting audio: {e}")
            return False
    
    def get_video_thumbnail(self, url, output_path):
        """Download TikTok video thumbnail"""
        try:
            video_info = self.get_video_info(url)
            if not video_info or not video_info.get('thumbnail'):
                return None
            
            import requests
            from urllib.parse import urlparse
            
            thumbnail_url = video_info['thumbnail']
            
            # Get file extension from URL
            parsed_url = urlparse(thumbnail_url)
            ext = os.path.splitext(parsed_url.path)[1] or '.jpg'
            
            # Create filename
            title = sanitize_filename(video_info.get('title', 'thumbnail'))
            filename = f"{title}_thumbnail{ext}"
            filepath = os.path.join(output_path, filename)
            
            # Download thumbnail
            response = requests.get(thumbnail_url, timeout=30)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error downloading thumbnail: {e}")
            return None
    
    def is_valid_tiktok_url(self, url):
        """Check if URL is a valid TikTok URL"""
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                return info is not None
        except:
            return False
    
    def resolve_short_url(self, url):
        """Resolve TikTok short URLs (vm.tiktok.com, etc.)"""
        try:
            if 'vm.tiktok.com' in url or 'tiktok.com/t/' in url:
                response = requests.head(url, allow_redirects=True, timeout=10)
                return response.url
            return url
        except:
            return url
    
    def get_user_videos(self, username, max_videos=10):
        """Get videos from a TikTok user (limited functionality)"""
        try:
            user_url = f"https://www.tiktok.com/@{username}"
            
            ydl_opts = {
                'quiet': True,
                'extract_flat': True,
                'playlistend': max_videos
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(user_url, download=False)
                
                if info and 'entries' in info:
                    videos = []
                    for entry in info['entries']:
                        video_info = {
                            'id': entry.get('id'),
                            'title': entry.get('title'),
                            'url': entry.get('url'),
                            'thumbnail': entry.get('thumbnail')
                        }
                        videos.append(video_info)
                    return videos
            
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting user videos: {e}")
            return []
    
    def download_user_videos(self, username, output_path, max_videos=10, quality='best'):
        """Download videos from a TikTok user"""
        try:
            videos = self.get_user_videos(username, max_videos)
            
            if not videos:
                return False
            
            success_count = 0
            for video in videos:
                if self.is_cancelled:
                    break
                
                try:
                    if self.download_video(video['url'], output_path, quality):
                        success_count += 1
                except:
                    continue
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Error downloading user videos: {e}")
            return False
