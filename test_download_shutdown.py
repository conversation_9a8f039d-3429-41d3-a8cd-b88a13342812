#!/usr/bin/env python3
"""
Test shutdown during active download
"""

import sys
import time
import threading
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from src.core.download_manager import DownloadManager
import uuid

def test_download_shutdown():
    """Test shutdown while download is active"""
    print("🧪 Testing Shutdown During Download...")
    
    try:
        # Initialize download manager
        print("  📦 Initializing download manager...")
        download_manager = DownloadManager()
        download_manager.start()
        
        # Create a test download (small file)
        download_info = {
            'id': str(uuid.uuid4()),
            'url': 'https://httpbin.org/delay/10',  # 10 second delay
            'filename': 'test_file.json',
            'save_path': str(Path(__file__).parent / 'downloads'),
            'category': 'Others',
            'video_type': None,
            'quality': 'best',
            'status': 'pending',
            'progress': 0,
            'speed': 0,
            'size': 0,
            'downloaded': 0,
            'source': 'test'
        }
        
        print("  🚀 Starting download...")
        success = download_manager.add_download(download_info)
        
        if not success:
            print("  ❌ Failed to start download")
            return False
        
        print("  ⏳ Waiting 2 seconds for download to start...")
        time.sleep(2)
        
        print("  🛑 Testing shutdown during active download...")
        start_time = time.time()
        
        # Stop download manager
        download_manager.stop()
        
        end_time = time.time()
        shutdown_time = end_time - start_time
        
        print(f"  ✅ Shutdown completed in {shutdown_time:.2f} seconds")
        
        if shutdown_time < 5:  # Should shutdown quickly
            print("  🎉 Fast shutdown test PASSED!")
            return True
        else:
            print("  ❌ Shutdown took too long!")
            return False
        
    except Exception as e:
        print(f"  ❌ Test failed: {e}")
        return False

def test_forced_shutdown():
    """Test forced shutdown"""
    print("\n🧪 Testing Forced Shutdown...")
    
    def force_exit():
        time.sleep(3)
        print("  🔥 Forcing exit...")
        import os
        os._exit(0)
    
    # Start force exit timer
    force_thread = threading.Thread(target=force_exit, daemon=True)
    force_thread.start()
    
    print("  ⏳ Will force exit in 3 seconds if not completed...")
    
    # Simulate long-running operation
    time.sleep(5)  # This should not complete
    print("  ❌ Forced shutdown failed!")

if __name__ == "__main__":
    # Test shutdown during download
    success = test_download_shutdown()
    
    if success:
        print("\n✅ Download shutdown test PASSED!")
        print("The application can now shutdown quickly even during downloads!")
    else:
        print("\n❌ Download shutdown test FAILED!")
        
        # Test forced shutdown as backup
        test_forced_shutdown()
